<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.rule.grid.mapper.RuleFuncMapper">
    <resultMap type="RuleFunc" id="RuleFuncResult">
        <id property="id" column="id"/>
        <result property="funcName" column="func_name"/>
        <result property="funcGroup" column="func_group"/>
        <result property="funcVal" column="func_val"/>
        <result property="funcDesc" column="func_desc"/>
        <result property="funcSeq" column="func_seq"/>
        <result property="alias" column="alias"/>
    </resultMap>

    <sql id="selectList">
        id
        ,func_name,func_group,func_val,func_desc,func_seq,alias
    </sql>

    <select id="getAll" resultType="com.dcube.rule.grid.domain.RuleFunc">
        select f.id,
               f.func_name,
               f.func_group,
               f.func_scope,
               f.func_val,
               f.func_desc,
               f.func_seq,
               g.group_name,
               g.group_seq,
               f.alias
        from cube_rule_func f
                 left join cube_rule_func_group g on f.func_group = g.id
    </select>
</mapper>