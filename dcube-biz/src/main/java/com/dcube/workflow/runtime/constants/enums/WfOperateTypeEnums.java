package com.dcube.workflow.runtime.constants.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.dcube.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum WfOperateTypeEnums {
    /**
     * 提交
     */
    SUBMIT("0", "提交"),
    /**
     * 同意
     */
    AGREE("1", "同意"),
    /**
     * 撤回
     */
    WITHDRAW("2", "撤回"),
    /**
     * 退回
     */
    RETURN("3", "退回"),
    ;

    @EnumValue
    private final String state;

    /**
     * 中文描述
     */
    private final String cName;

    public static WfOperateTypeEnums getByName(String name) {
        return Arrays.stream(WfOperateTypeEnums.values())
                .filter(e -> StringUtils.equals(e.name(), name))
                .findFirst()
                .orElse(null);
    }

}
