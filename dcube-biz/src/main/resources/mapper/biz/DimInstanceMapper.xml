<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.biz.mapper.DimInstanceMapper">
    <resultMap id="BaseResultMap" type="com.dcube.biz.domain.DimInstance">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="dimDirectoryId" column="dim_directory_id" jdbcType="BIGINT"/>
        <result property="dimName" column="dim_name" jdbcType="VARCHAR"/>
        <result property="dimCode" column="dim_code" jdbcType="VARCHAR"/>
        <result property="indexNo" column="index_no" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="ancestors" column="ancestors" jdbcType="VARCHAR"/>
        <result property="isLeaf" column="is_leaf" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        dim_directory_id,
        dim_name,
        dim_code,
        index_no,
        parent_id,
        ancestors,
        is_leaf
    </sql>

    <select id="getMaxIndexNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_instance
        WHERE dim_directory_id = #{dimDirectoryId}
          AND parent_id = #{parentId}
          and index_no = (SELECT MAX(index_no)
                          FROM cube_dim_instance
                          WHERE dim_directory_id = #{dimDirectoryId}
                            AND parent_id = #{parentId})
        LIMIT 1
    </select>

    <select id="getPreviousDimInstanceByAncestorAndIndexNo" resultType="com.dcube.biz.domain.DimInstance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_instance
        WHERE dim_directory_id = #{dimDirectoryId}
          AND parent_id = #{parentId}
          AND index_no = (SELECT MAX(s.index_no)
                          FROM cube_dim_instance s
                          WHERE s.dim_directory_id = #{dimDirectoryId}
                            AND s.parent_id = #{parentId}
                            AND s.index_no &lt; #{indexNo})
        LIMIT 1
    </select>

    <select id="getNextDimInstanceByAncestorAndIndexNo" resultType="com.dcube.biz.domain.DimInstance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_instance
        WHERE dim_directory_id = #{dimDirectoryId}
          AND parent_id = #{parentId}
          AND index_no = (SELECT MIN(s.index_no)
                          FROM cube_dim_instance s
                          WHERE s.dim_directory_id = #{dimDirectoryId}
                            AND s.parent_id = #{parentId}
                            AND s.index_no &gt; #{indexNo})
        LIMIT 1
    </select>

    <select id="selectSubById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_instance
        WHERE find_in_set(#{id}, ancestors)
    </select>

    <select id="selectSubById" resultMap="BaseResultMap" databaseId="pg">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_instance
        WHERE #{id} = ANY (string_to_array(ancestors, ','):: int [])
    </select>
</mapper>