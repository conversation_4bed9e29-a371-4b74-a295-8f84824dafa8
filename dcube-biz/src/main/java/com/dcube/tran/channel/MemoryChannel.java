package com.dcube.tran.channel;

import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class MemoryChannel extends Channel {

    private int bufferSize = 0;

    private final AtomicInteger memoryBytes = new AtomicInteger(0);

    private ArrayBlockingQueue<Record> queue = null;

    private final ReentrantLock lock;

    private final Condition notInsufficient;
    private final Condition notEmpty;

    public MemoryChannel() {
        super();
        this.queue = new ArrayBlockingQueue<>(this.getCapacity());
        this.bufferSize = 64 * 1024 * 1024;

        lock = new ReentrantLock();
        notInsufficient = lock.newCondition();
        notEmpty = lock.newCondition();
    }

    @Override
    public void close() {
        super.close();
        try {
            this.queue.put(TerminateRecord.get());
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public void clear() {
        this.queue.clear();
    }

    @Override
    protected void doPush(Record r) {
        try {
            this.queue.put(r);
            memoryBytes.addAndGet(r.getMemorySize());
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            log.error("中断");
        }
    }

    @Override
    protected boolean doPush(Record r, long second) {
        try {
            boolean b = this.queue.offer(r, second, TimeUnit.SECONDS);
            if (b) {
                memoryBytes.addAndGet(r.getMemorySize());
            }
            return b;
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    @Override
    protected void doPushAll(Collection<Record> rs) {
        if (rs == null || rs.isEmpty()) return;
        try {
            lock.lockInterruptibly();
            if (!tryPushBatch(rs)) {
                int adaptiveBatchSize = 64; // 可调整
                Iterator<Record> it = rs.iterator();
                List<Record> batch = new ArrayList<>();
                boolean success = true;
                while (it.hasNext()) {
                    if (!success) {
                        while (!batch.isEmpty()) {
                            //当前数据按限制条数可分为多少批次
                            int batchSize = batch.size() / adaptiveBatchSize;
                            if (batchSize > 1) {
                                List<Record> partList;
                                for (int i = 0; i < batchSize; i++) {
                                    // 截取批次长度的list
                                    partList = batch.subList(0, adaptiveBatchSize);
                                    // 分批业务逻辑处理
                                    success = tryPushBatch(partList);
                                    if (success) {
                                        // 去除已经处理的部分
                                        partList.clear();
                                    } else {
                                        notInsufficient.await(200, TimeUnit.MILLISECONDS);
                                    }
                                }
                                // 获取最后一次截取后的剩余列表数据
                                if (!batch.isEmpty() && batch.size() <= adaptiveBatchSize) {
                                    // 业务逻辑数据处理
                                    success = tryPushBatch(batch);
                                    if (success) {
                                        // 去除已经处理的部分
                                        batch.clear();
                                    } else {
                                        notInsufficient.await(200, TimeUnit.MILLISECONDS);
                                    }
                                }
                            } else {
                                success = tryPushBatch(batch);
                                if (success) {
                                    batch.clear();
                                } else {
                                    adaptiveBatchSize = Math.max(adaptiveBatchSize >> 1, 1);
                                    notInsufficient.await(200, TimeUnit.MILLISECONDS);
                                }
                            }
                        }
                    }
                    Record r = it.next();
                    batch.add(r);

                    // 当前批次达到最大批次限制，尝试写入
                    if (batch.size() >= adaptiveBatchSize) {
                        success = tryPushBatch(batch);
                        if (success) {
                            adaptiveBatchSize = Math.min(adaptiveBatchSize << 1, 1024);
                            batch.clear();
                        } else {
                            adaptiveBatchSize = Math.max(adaptiveBatchSize >> 1, 1);
                            notInsufficient.await(200, TimeUnit.MILLISECONDS);
                        }
                    }
                }

                // 处理剩余未提交的数据
                while (!batch.isEmpty()) {
                    //当前数据按限制条数可分为多少批次
                    int batchSize = batch.size() / adaptiveBatchSize;
                    if (batchSize > 1) {
                        List<Record> partList;
                        for (int i = 0; i < batchSize; i++) {
                            // 截取批次长度的list
                            partList = batch.subList(0, adaptiveBatchSize);
                            // 分批业务逻辑处理
                            success = tryPushBatch(partList);
                            if (success) {
                                // 去除已经处理的部分
                                partList.clear();
                            } else {
                                notInsufficient.await(200, TimeUnit.MILLISECONDS);
                            }
                        }
                        // 获取最后一次截取后的剩余列表数据
                        if (!batch.isEmpty() && batch.size() <= adaptiveBatchSize) {
                            // 业务逻辑数据处理
                            success = tryPushBatch(batch);
                            if (success) {
                                // 去除已经处理的部分
                                batch.clear();
                            } else {
                                notInsufficient.await(200, TimeUnit.MILLISECONDS);
                            }
                        }
                    } else {
                        success = tryPushBatch(batch);
                        if (success) {
                            batch.clear();
                        } else {
                            adaptiveBatchSize = Math.max(adaptiveBatchSize >> 1, 1);
                            notInsufficient.await(200, TimeUnit.MILLISECONDS);
                        }
                    }
                }

            }
        } catch (InterruptedException e) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, e);
        } finally {
            lock.unlock();
        }
    }

    private boolean validateMemory(int bytes, int size) {
        return memoryBytes.get() + bytes <= this.byteCapacity && queue.remainingCapacity() >= size;
    }

    private boolean tryPushBatch(Collection<Record> batch) {
        int recordBytes = getRecordBytes(batch);
        if (validateMemory(recordBytes, batch.size())) {
            queue.addAll(batch);
            memoryBytes.addAndGet(recordBytes);
            notEmpty.signalAll();
            return true;
        }
        return false;
    }

    @Override
    protected Record doPull() {
        try {
            Record r = this.queue.take();
            memoryBytes.addAndGet(-r.getMemorySize());
            // 加上这个唤醒生产者线程
            signalNotFull();
            return r;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e);
        }
    }

    private void signalNotFull() {
        try {
            lock.lockInterruptibly();
            notInsufficient.signalAll();
        } catch (InterruptedException e) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    protected Record doPull(long seconds) {
        try {
            Record r = this.queue.poll(seconds, TimeUnit.SECONDS);
            if (r != null) {
                memoryBytes.addAndGet(-r.getMemorySize());
                // 加上这个唤醒生产者线程
                signalNotFull();
            }
            return r;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e);
        }
    }


    @Override
    protected void doPullAll(Collection<Record> rs) {
        assert rs != null;
        rs.clear();
        final int bytes = getRecordBytes(rs);
        try {
            lock.lockInterruptibly();
            while (this.queue.drainTo(rs, bufferSize) <= 0) {
                notEmpty.await(200L, TimeUnit.MILLISECONDS);
            }
            memoryBytes.addAndGet(-bytes);
            notInsufficient.signalAll();
        } catch (InterruptedException e) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, e);
        } finally {
            lock.unlock();
        }
    }


    private int getRecordBytes(Collection<Record> rs) {
        return rs.stream().mapToInt(Record::getMemorySize).sum();
    }

    @Override
    public int size() {
        return this.queue.size();
    }

    @Override
    public boolean isEmpty() {
        return this.queue.isEmpty();
    }

}
