package com.dcube.workflow.runtime.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.CellDto;
import com.dcube.biz.dto.RowDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.vo.MemTableDataVo;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.workflow.define.constants.enums.FlowNodeTypeEnums;
import com.dcube.workflow.define.domain.WfDefine;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.define.service.IWfDefineService;
import com.dcube.workflow.define.utils.FlowChartUtils;
import com.dcube.workflow.runtime.constants.WfRtConstants;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfOperateTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.domain.WfVariable;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.service.IWfVariableService;
import com.dcube.workflow.runtime.strategy.ApproveStrategy;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 审批通过
 */
@Service("AGREE")
public class AgreeStrategyImpl implements ApproveStrategy {

    @Autowired
    private IWfInstanceService wfInstanceService;
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private IWfDefineService wfDefineService;
    @Autowired
    private ITableService tableService;
    @Autowired
    private IWfVariableService wfVariableService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult approve(WfTaskDTO wfTaskDto) {
        Assert.notNull(wfTaskDto.getId(), "任务主键id不能为空");
        WfTask wfTask = wfTaskService.getById(wfTaskDto.getId());
        Assert.notNull(wfTask, "未查询到任务");
        Long instanceId = wfTaskDto.getInstanceId();
        if (instanceId == null) {
            instanceId = wfTask.getInstanceId();
            wfTaskDto.setInstanceId(instanceId);
        }
        WfInstance wfInstance = wfInstanceService.getById(instanceId);
        Assert.notNull(wfInstance, "未查询到流程实例");
        if (wfInstance.getInstanceState() != WfInstanceStateEnums.RUNNING) {
            throw new WfRuntimeException("流程实例状态不为运行中，无法撤回");
        }
        switch (wfTask.getFlowType()) {
            case TABLE:
                doExecuteTableTask(wfTaskDto, wfInstance);
                break;
            case CUBE:
                // 归口审批

                wfTask.setCommentContent(wfTaskDto.getCommentContent());
                wfTask.setCentralizeDimDefineId(wfTaskDto.getCentralizeDimDefineId());
                doExecuteCubeTask(wfTask, wfInstance);
                break;
        }
        return AjaxResult.success("审批通过成功");
    }

    private void doExecuteTableTask(WfTaskDTO wfTaskDto, WfInstance wfInstance) {
        WfDefine wfDefine = wfDefineService.getById(wfInstance.getDefineId());
        Assert.notNull(wfInstance, "未查询到流程定义");
        FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
        FlowChart.NodeDTO nextNode = FlowChartUtils.getTargetNodeByEdgeId(flowChart, wfTaskDto.getNextEdgeId());
        if (nextNode == null) {
            throw new WfRuntimeException("未找到下一个节点");
        }
        Long tableId = wfDefine.getTableId();
        Table table = tableService.getById(tableId);
        if (table == null) {
            throw new WfRuntimeException("未查询到二维表");
        }
        FlowChart.NodeDTO.PropertiesDTO nextNodeProperties = nextNode.getProperties();
        String unitColumnCode = nextNodeProperties.getUnitColumnCode();
        String tableMeta = table.getTableMeta();
        List<TableMetaJson> tableMetaJson = StringUtils.isEmpty(tableMeta) ? Collections.emptyList() : JSON.parseArray(table.getTableMeta(), TableMetaJson.class);
        int unitColumn = 0;
        boolean unitColumnFlag = false;
        String wfStateColumnCode = wfDefine.getFlowStateColumnCode();
        int wfStateColumn = 0;
        boolean wfStateColumnFlag = false;
        for (TableMetaJson tableMetaJsonItem : tableMetaJson) {
            if (StringUtils.equals(tableMetaJsonItem.getCode(), unitColumnCode)) {
                unitColumnFlag = true;
            }
            if (!unitColumnFlag) {
                unitColumn++;
            }
            if (StringUtils.equals(tableMetaJsonItem.getCode(), wfStateColumnCode)) {
                wfStateColumnFlag = true;
            }
            if (!wfStateColumnFlag) {
                wfStateColumn++;
            }
            if (unitColumnFlag && wfStateColumnFlag) {
                break;
            }
        }
        if (!wfStateColumnFlag) {
            throw new WfRuntimeException("未找到二维表流程状态列");
        }
        String dataId = wfTaskDto.getDataId();
        String unitColumnVal = "";
        TableListQuery tableListQuery = new TableListQuery();
        tableListQuery.setTableId(Math.toIntExact(tableId));
        tableListQuery.setFilterDataId(dataId);
        tableListQuery.setCurrentPage(1);
        tableListQuery.setPageSize(dataId.split(",").length);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PageInfo<MemTableDataVo> pageInfo = tableService.queryTableData(tableListQuery, loginUser);
        if (pageInfo == null
                || CollectionUtils.isEmpty(pageInfo.getList())
                || pageInfo.getList().get(0) == null
                || CollectionUtils.isEmpty(pageInfo.getList().get(0).getRows())) {
            throw new WfRuntimeException("未查询到二维表数据");
        }
        MemTableDataVo memTableDataVo = pageInfo.getList().get(0);
        if (nextNode.getType() == FlowNodeTypeEnums.TASK) {
            if (!unitColumnFlag) {
                throw new WfRuntimeException("未找到二维表单位列");
            }
            for (RowDto rowDto : memTableDataVo.getRows()) {
                if (rowDto == null || CollectionUtils.isEmpty(rowDto.getCells()) || rowDto.getCells().size() <= unitColumn || rowDto.getCells().get(unitColumn) == null) {
                    throw new WfRuntimeException("未查询到二维表单位列数据");
                }
                CellDto cellDto = rowDto.getCells().get(unitColumn);
                if (StringUtils.isEmpty(unitColumnVal) && cellDto.getOriginalValue() != null) {
                    unitColumnVal = String.valueOf(cellDto.getOriginalValue());
                } else {
                    if (!StringUtils.equals(unitColumnVal, String.valueOf(cellDto.getOriginalValue()))) {
                        throw new WfRuntimeException("不能提交不同单位的数据");
                    }
                }
            }
        }

        FlowChart.EdgeDTO edge = FlowChartUtils.getEdgeById(flowChart, wfTaskDto.getNextEdgeId());
        if (edge == null) {
            throw new WfRuntimeException("未找到下一个线");
        }
        wfTaskDto.setNextEdgeName(edge.getProperties().getText());
        wfTaskDto.setNextNodeId(nextNode.getId());
        wfTaskDto.setNextNodeName(nextNodeProperties.getText());
        // 完成任务
        wfTaskService.handle(wfTaskDto);
        // 执行计算规则
        wfTaskService.executeWFComputeRule(tableId, dataId, edge);
        // 进入下一环节
        wfTaskService.enterNextNode(wfInstance, nextNode, table.getMemTableName(), table.getChildFlag(), table.getTableLevel(), memTableDataVo.getRows(), unitColumnVal, wfStateColumn);
    }

    private void doExecuteCubeTask(WfTask wfTask, WfInstance wfInstance) {
        //归口节点-同意：该归口节点状态必须是“未处理”；提交上来的“待处理节点”状态必须是“待审批”
        //其它节点-同意：“待处理节点”状态必须是“待审批”；如果该节点配置有“归口节点”，则：归口节点状态须全部是“已审批”。否则提示：归口节点审批未完全通过，不能同意！
        if (wfTask.getCentralizeDimDefineId() == null) {
            if (wfTask.getTaskState() != WfTaskStateEnums.PRE_APPROVE) {
                throw new WfRuntimeException("该任务状态不为待审批，无法审批");
            }
            // 校验归口是否审批完成
            long count = wfVariableService.count(Wrappers.<WfVariable>lambdaQuery()
                    .eq(WfVariable::getInstanceId, wfInstance.getId())
                    .eq(WfVariable::getNodeId, wfTask.getNodeId())
                    .isNotNull(WfVariable::getCentralizeId)
                    .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                    .ne(WfVariable::getVariableVal, WfTaskStateEnums.APPROVED.name())
            );
            if (count > 0) {
                throw new WfRuntimeException("归口节点审批未完全通过，不能同意！");
            }
        } else {
            WfVariable wfVariable = wfVariableService.getOne(Wrappers.<WfVariable>lambdaQuery()
                    .eq(WfVariable::getInstanceId, wfInstance.getId())
                    .eq(WfVariable::getNodeId, wfTask.getNodeId())
                    .eq(WfVariable::getCentralizeId, wfTask.getCentralizeDimDefineId())
                    .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                    .last("limit 1")
            );
            Assert.notNull(wfVariable, "未查询到归口节点审批状态流程变量");
            // 归口审批
            if (!StringUtils.equals(wfVariable.getVariableVal(), WfTaskStateEnums.PRE_HANDLE.name())) {
                throw new WfRuntimeException("该任务状态不为待处理，无法审批");
            }
        }

        wfTask.setOperateUserId(String.valueOf(SecurityUtils.getUserId()));
//        wfTask.setTaskDone("1");
        wfTask.setApproveTime(new Date());
        wfTask.setTaskState(WfTaskStateEnums.APPROVED);
        wfTask.setCost(DateUtil.betweenMs(wfTask.getApproveTime(), wfTask.getCreateTime()));
        wfTask.setOperateType(WfOperateTypeEnums.AGREE);
        // 完成任务
        wfTaskService.handle(wfTask);
        // 流程变量
        LambdaUpdateWrapper<WfVariable> wfVariableLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        // 归口节点审批
        if (wfTask.getCentralizeDimDefineId() == null) {
            wfVariableLambdaUpdateWrapper.isNull(WfVariable::getCentralizeId);
        } else {
            wfVariableLambdaUpdateWrapper.eq(WfVariable::getCentralizeId, wfTask.getCentralizeDimDefineId());
        }
        wfVariableLambdaUpdateWrapper.eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                .eq(WfVariable::getNodeId, wfTask.getNodeId())
                .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                .set(WfVariable::getVariableVal, wfTask.getTaskState().name());
        wfVariableService.update(wfVariableLambdaUpdateWrapper);
        if (wfTask.getCentralizeDimDefineId() != null) {
            // 校验归口是否全部审批完成
            long count = wfVariableService.count(Wrappers.<WfVariable>lambdaQuery()
                    .eq(WfVariable::getInstanceId, wfInstance.getId())
                    .eq(WfVariable::getCentralizeId, wfTask.getCentralizeDimDefineId())
                    .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                    .ne(WfVariable::getVariableVal, WfTaskStateEnums.APPROVED.name())
            );
            if (count == 0) {
                // 完成任务
                WfTask task = wfTaskService.getOne(Wrappers.<WfTask>lambdaQuery()
                        .eq(WfTask::getInstanceId, wfInstance.getId())
                        .eq(WfTask::getCentralizeDimDefineId, wfTask.getCentralizeDimDefineId())
                );
                Assert.notNull(task, "归口审批任务为空");
                task.setApproveTime(new Date());
                task.setTaskDone("1");
                task.setTaskState(WfTaskStateEnums.APPROVED);
                task.setCost(DateUtil.betweenMs(task.getApproveTime(), task.getCreateTime()));
                task.setOperateUserId(String.valueOf(SecurityUtils.getUserId()));
                wfTaskService.updateById(task);
            }
        }
    }

}
