<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.biz.mapper.TableMapper">
    <select id="getSubTablesById" resultType="com.dcube.biz.domain.Table">
        SELECT id,
               parent_id,
               directory_id,
               table_name,
               create_mode,
               view_id,
               child_flag,
               status,
               type,
               ancestors,
               mem_table_name,
               create_by,
               create_time,
               update_by,
               update_time,
               table_level
        FROM cube_2d_table
        WHERE find_in_set(#{id}, ancestors)
    </select>

    <select id="getSubTablesById" resultType="com.dcube.biz.domain.Table" databaseId="pg">
        SELECT id,
               parent_id,
               directory_id,
               table_name,
               create_mode,
               view_id,
               child_flag,
               status,
               type,
               ancestors,
               mem_table_name,
               create_by,
               create_time,
               update_by,
               update_time,
               table_level
        FROM cube_2d_table
        WHERE #{id} = ANY (string_to_array(ancestors, ','):: int [])
    </select>
</mapper>