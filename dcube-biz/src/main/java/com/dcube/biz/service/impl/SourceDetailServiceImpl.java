package com.dcube.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.ExcelReader;
import cn.idev.excel.FastExcel;
import cn.idev.excel.cache.selector.SimpleReadCacheSelector;
import cn.idev.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.SourceDetail;
import com.dcube.biz.dto.SourceDetailDto;
import com.dcube.biz.dto.SourceDto;
import com.dcube.biz.dto.SourceResolvingDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.listener.ExcelFastReadListener;
import com.dcube.biz.mapper.SourceDetailMapper;
import com.dcube.biz.service.ISourceDetailService;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.util.TableUtils;
import com.dcube.common.constant.Constants;
import com.dcube.common.constant.enums.YNV2Enums;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.CryptoUtil;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUtils;
import com.dcube.common.utils.poi.Column;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.type.SqlTypeName;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @创建人 zhouhx
 * @创建时间 2024/1/8 11:42
 * @描述
 */
@Service
@Slf4j
public class SourceDetailServiceImpl extends ServiceImpl<SourceDetailMapper, SourceDetail> implements ISourceDetailService {

    @Autowired
    private ISourceService sourceService;
    @Autowired
    private CryptoUtil cryptoUtil;

    private final Gson gson = new Gson();

    @Override
    public List<SourceDetail> queryListBySourceId(String sourceId) {
        QueryWrapper<SourceDetail> qw = new QueryWrapper<>();
        qw.lambda().eq(SourceDetail::getSourceId, sourceId);
        qw.lambda().orderByDesc(SourceDetail::getCreateTime);
        return this.list(qw);
    }

    @Override
    public List<SourceDetailDto> fetchingTextHead(SourceResolvingDto source) {
        if (Objects.isNull(source)) {
            return Collections.emptyList();
        }

        SourceDto dbSource = sourceService.getSourceById(source.getId());
        if (dbSource == null) {
            throw new ServiceException("未查询到数据源");
        }
        AbstractRepository repository = RepositoryFactory.getRepository(dbSource.getSourceType().toUpperCase(), dbSource.getSourceConfig(), BizConstants.DATA_SOURCE_DETAIL_PREFIX + "%");
        Map<String, List<Map<String, String>>> tableNameColumnCommentsMap;
        try {
            tableNameColumnCommentsMap = repository.getTableNameColumnCommentsMap(repository.getCatalog(), null);
        } catch (SQLException e) {
            log.error("数据库操作失败", e);
            throw new ServiceException("数据库操作失败。");
        }
        if (MapUtil.isEmpty(tableNameColumnCommentsMap)) {
            log.info("未查询到以{}为前缀的数据库表信息。", BizConstants.DATA_SOURCE_DETAIL_PREFIX);
            return Collections.emptyList();
        }
        // 当前日期格式化为文件目录名，如 20231229 或 2023-12-29
        String dateDir = DateUtil.format(DateUtil.offsetDay(new Date(), source.getDataOwnershipPeriodType().getOffset()), source.getDirDateFormat());

        // 构建当前日期的数据文件所在路径：/data/20230731/xxx.txt
        String dataFilePathName = source.getDataFileDir() + FileUtil.FILE_SEPARATOR + dateDir + FileUtil.FILE_SEPARATOR;

        // 根据文件后缀过滤指定目录下的所有文件
        Map<String, File> fileMap = FileUtils.lsFilesWithFilter(dataFilePathName, tableNameColumnCommentsMap.keySet().stream().map(tableName -> StringUtils.removeStart(tableName, BizConstants.DATA_SOURCE_DETAIL_PREFIX)).collect(Collectors.toSet()), source.getDataFileSuffix(), source.getFileNamePrefixNum(), source.getFileNameSuffixNum());
        if (MapUtil.isEmpty(fileMap)) {
            log.error("指定的目录：{}，找不到数据文件。", dataFilePathName);
            return Collections.emptyList();
        }
        List<SourceDetailDto> details = new ArrayList<>(fileMap.size());

        Path dataFilePath = Paths.get(dataFilePathName);
        for (Map.Entry<String, File> entry : fileMap.entrySet()) {
            File file = entry.getValue();
            // 校验ok文件是否存在
            String prefix = FilenameUtils.getBaseName(file.getName());
            String okFileName = prefix + "." + source.getOkFileSuffix();
            Path okFilePath = dataFilePath.resolve(okFileName);
            File okFile = okFilePath.toFile();
            boolean exist = FileUtil.exist(okFile);
            if (!exist) {
                log.info("校验文件不存在：{}", okFileName);
                continue;
            }
//            // 为每个文件构建一个对应的 SourceDetailDto
//            String tableName = TableUtils.getRandomName(
//                    Constants.TABLE_PREFIX,
//                    FileUtils.getNameNotSuffix(file.getName()),
//                    Constants.TABLE_RANDOM_LENGTH
//            );
            String tableName = BizConstants.DATA_SOURCE_DETAIL_PREFIX + entry.getKey();
            List<Map<String, String>> columnList = MapUtils.getObject(tableNameColumnCommentsMap, tableName, Collections.emptyList());
            int dbFieldCount = columnList.size();
            SourceDetailDto detail = new SourceDetailDto();
            detail.setSourceId(source.getId());
            detail.setFileName(file.getName());
            detail.setDbFieldCount(dbFieldCount);
            detail.setDbTableName(tableName);
//            detail.setTableName(tableName);
            // 检测文件编码格式（如 UTF-8、GBK）
            Charset charset = FileUtils.detectCharset(file);
            try (// 使用 RandomAccessFile 随机读取第一行（表头）
                 RandomAccessFile raf = new RandomAccessFile(file.getAbsoluteFile(), "r");
                 RandomAccessFile okFileRaf = new RandomAccessFile(okFile, "r")) {
                String textHead = FileUtil.readLine(raf, charset);
                if (StringUtils.isNotEmpty(textHead)) {
                    // 以指定分隔符分割表头字段
                    String[] fieldNames = textHead.split(source.getSplitter());
                    detail.setFieldCount(fieldNames.length); // 字段个数

//                    List<TableMetaJson> metadata = new ArrayList<>(fieldNames.length);
//                    int no = 0;
//                    for (String fieldName : fieldNames) {
//                        TableMetaJson tableMetadata = new TableMetaJson();
//                        tableMetadata.setNo(no + 1);
//
//                        tableMetadata.setCode(fieldName);
//                        tableMetadata.setName(fieldName);
//                        //添加原始字段名称
//                        tableMetadata.setOriginalColumnCode(fieldName);
//                        if (DataFileTypeEnum.Dat.getCode().equals(source.getDataFileType())) {
//                            tableMetadata.setCode("column_" + (no + 1));
//                            tableMetadata.setName("字段" + (no + 1));
//                            //添加原始字段名称
//                            tableMetadata.setOriginalColumnCode("column_" + (no + 1));
//                        }
//
//                        tableMetadata.setOldColumnType(SqlTypeName.VARCHAR.getName());
//                        tableMetadata.setNewColumnType(SqlTypeName.VARCHAR.getName());
//                        metadata.add(tableMetadata);
//                        no++;
//                    }
//                    detail.setTableMeta(metadata);
                }

                // 读取文件第2行到第11行内容作为预览数据（跳过表头）
                boolean skipFistRow = YNV2Enums.Y == detail.getSkipFistRow();
                String content = FileUtils.readLinesByRange(skipFistRow ? 2 : 1, Constants.PREVIEW_COUNT + 1, file.getAbsolutePath(), charset);
                if (StringUtils.isNotEmpty(content)) {
                    Map<String, Object> result = Maps.newHashMapWithExpectedSize(3);

                    // 获取文件总行数，设置为预览信息的一部分
                    String totalLines = FileUtil.readLine(okFileRaf, charset);
                    result.put("totalCount", totalLines);
//                    List<TableMetaJson> tableMeta = detail.getTableMeta();
//                    if (CollectionUtil.isNotEmpty(tableMeta)) {
                    if (CollectionUtils.isNotEmpty(columnList)) {
                        log.info("开始处理文件:{}", file.getName());
                        // 将每一行转为 Map，每个字段映射为 code:value
                        List<String> rows = StrUtil.splitTrim(content, "\n");
                        List<Map<String, String>> values = new ArrayList<>(rows.size());
                        for (String row : rows) {
                            List<String> fieldValues = StrUtil.split(row, source.getSplitter());
                            int fieldSize = fieldValues.size();
                            Map<String, String> colData = Maps.newHashMapWithExpectedSize(dbFieldCount);
                            for (int j = 0; j < dbFieldCount; j++) {
                                if (j >= fieldSize) {
                                    colData.put(MapUtils.getString(columnList.get(j), "columnName"), null);
                                } else {
                                    colData.put(MapUtils.getString(columnList.get(j), "columnName"), fieldValues.get(j));
                                }
                            }
                            if (fieldSize >= dbFieldCount) {
                                for (int j = dbFieldCount, k = 1; j < fieldSize; j++) {
                                    colData.put("Column" + k,  fieldValues.get(j));
                                }
                            }
                            values.add(colData);
                        }
                        result.put("result", values);
                        result.put("columnList", columnList);
                    }
                    detail.setPreviewData(result);
                }
            } catch (IOException e) {
                log.error("文件解析失败，文件路径：{}", file.getAbsoluteFile(), e);
                throw new ServiceException(String.format("文件解析失败，文件路径：%s", file.getAbsoluteFile()));
            }
            details.add(detail);
        }
        return details;
    }

    @Override
    public List<SourceDetailDto> fetchingExcelHead(SourceResolvingDto source) {
        List<SourceDetailDto> details = new ArrayList<>();
        if (Objects.nonNull(source)) {
            if (!FileUtil.exist(source.getFilePath())) {
                log.error(String.format("找不到文件，文件路径：%s", source.getFilePath()));
                throw new ServiceException(String.format("找不到文件，文件路径：%s", source.getFilePath()));
            }
            SourceDetailDto detail = new SourceDetailDto();
            Map<String, Object> result = new HashMap<>();
            SimpleReadCacheSelector simpleReadCacheSelector = new SimpleReadCacheSelector();
            simpleReadCacheSelector.setMaxUseMapCacheSize(5L);
            simpleReadCacheSelector.setMaxCacheActivateBatchCount(20);
            ExcelReader excelReader = FastExcel.read(source.getFilePath()).readCacheSelector(simpleReadCacheSelector).build();
            ReadSheet sheet = FastExcel.readSheet(0).registerReadListener(new ExcelFastReadListener(result, Constants.PREVIEW_COUNT)).build();
            excelReader.read(sheet);
            if (MapUtil.isNotEmpty(result)) {
                String tableName = TableUtils.getRandomName(Constants.TABLE_PREFIX, FileUtils.getNameNotSuffix(source.getSourceName()), Constants.TABLE_RANDOM_LENGTH);
                detail.setSourceId(source.getId());
                detail.setFileName(source.getFilePath());
                detail.setTableName(tableName);
                if (Objects.nonNull(result.get("columns")) && CollectionUtil.isNotEmpty((List<Column>) result.get("columns"))) {
                    List<Column> columns = (List<Column>) result.get("columns");
                    detail.setFieldCount(columns.size());
                    List<TableMetaJson> metadata = new ArrayList<>();
                    int no = 0;
                    for (Column column : columns) {
                        TableMetaJson tableMetadata = new TableMetaJson();
                        tableMetadata.setNo(no + 1);
                        tableMetadata.setCode(column.getName());
                        tableMetadata.setName(column.getName());
                        tableMetadata.setOriginalColumnCode(column.getName());
                        switch (column.getType()) {
                            case STRING:
                                tableMetadata.setOldColumnType(SqlTypeName.VARCHAR.getName());
                                tableMetadata.setNewColumnType(SqlTypeName.VARCHAR.getName());
                                break;
                            case NUMERIC:
                                tableMetadata.setOldColumnType(SqlTypeName.DOUBLE.getName());
                                tableMetadata.setNewColumnType(SqlTypeName.DOUBLE.getName());
                                break;
                            case DATE:
                                tableMetadata.setOldColumnType(SqlTypeName.DATE.getName());
                                tableMetadata.setNewColumnType(SqlTypeName.DATE.getName());
                                break;
                            case BOOLEAN:
                                tableMetadata.setOldColumnType(SqlTypeName.BOOLEAN.getName());
                                tableMetadata.setNewColumnType(SqlTypeName.BOOLEAN.getName());
                                break;
                            default:
                                tableMetadata.setOldColumnType(SqlTypeName.VARCHAR.getName());
                                tableMetadata.setNewColumnType(SqlTypeName.VARCHAR.getName());
                        }
                        metadata.add(tableMetadata);
                        no++;
                    }
                    detail.setTableMeta(metadata);
                    detail.setPreviewData(result);//预览数据
                }
            }
            details.add(detail);
        }
        return details;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSourceDetail(SourceDto source) {
        if (Objects.nonNull(source)) {
            if (CollectionUtil.isEmpty(source.getDetails())) {
                log.error("数据库列表为空。");
                return true;
            }
            SourceDto dbSource = sourceService.getSourceById(source.getId());
            if (dbSource == null) {
                throw new ServiceException("未查询到数据源");
            }
            //先删除再新增
            this.removeSourceDetail(CollectionUtil.toList(source.getId()));
            List<SourceDetail> sourceDetails = new ArrayList<>(source.getDetails().size());
            //验证表名是否已存在
            for (SourceDetailDto detail : source.getDetails()) {
                SourceDetail sourceDetail = new SourceDetail();
                BeanUtil.copyProperties(detail, sourceDetail);
                sourceDetail.setSourceId(source.getId());
                sourceDetail.setTableMeta(gson.toJson(detail.getTableMeta()));
                AbstractRepository repository = RepositoryFactory.getRepository(dbSource.getSourceType().toUpperCase(), dbSource.getSourceConfig(), detail.getTableName());
                try {
                    //生成表
                    TableMetaData tableMetaData = MemGridUtils.parseColumn(detail.getTableName(), detail.getRealTableName(), detail.getTableMeta());
                    repository.syncTable(repository.getCatalog(), tableMetaData);
                } catch (SQLException e) {
                    log.error("数据库操作异常。");
                    throw new ServiceException("数据库操作异常。");
                }
                sourceDetails.add(sourceDetail);
            }
            return this.saveBatch(sourceDetails);
        }
        return true;
    }

    /**
     * 删除数据源明细
     * @param sourceIds
     * @return
     */
    @Override
    public Boolean removeSourceDetail(List<String> sourceIds) {
        QueryWrapper<SourceDetail> qw = new QueryWrapper<>();
        qw.lambda().in(SourceDetail::getSourceId, sourceIds);
        List<SourceDetail> details = this.list(qw);
        this.remove(qw);
        return dropTableBySource(details);
    }

    /**
     * 删除数据源明细对应的表
     * @param details
     * @return
     */
    public Boolean dropTableBySource(List<SourceDetail> details) {
        if (CollectionUtil.isNotEmpty(details)) {
            for (SourceDetail detail : details) {
                Source source = sourceService.getById(detail.getSourceId());
                if (Objects.nonNull(source)) {
                    SourceConfigJson sourceConfig = gson.fromJson(source.getSourceConfig(), SourceConfigJson.class);
                    AbstractRepository repository = RepositoryFactory.getRepository(source.getSourceType().toUpperCase(), sourceConfig, detail.getTableName());
                    try {
                        boolean exists = repository.existsTableCheck(repository.getCatalog(), null);
                        if (exists) {
                            //删除表
                            repository.executeSql(String.format("DROP TABLE %s", detail.getTableName()));
                        }
                    } catch (SQLException e) {
                        log.error("数据库操作异常。");
                        throw new ServiceException("数据库操作异常。");
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Boolean clearTableData(String sourceId) {
        if (StringUtils.isEmpty(sourceId)) {
            log.error("数据源Id不能为空。");
            throw new ServiceException("数据源Id不能为空。");
        }
        List<SourceDetail> sourceDetails = this.queryListBySourceId(sourceId);
        if (CollectionUtils.isEmpty(sourceDetails)) {
            log.error("数据源明细为空。");
            throw new ServiceException("数据源明细为空。");
        }
        for (SourceDetail detail : sourceDetails) {
            Source source = sourceService.getById(detail.getSourceId());
            if (Objects.nonNull(source)) {
                SourceConfigJson sourceConfig = gson.fromJson(source.getSourceConfig(), SourceConfigJson.class);
                AbstractRepository repository = RepositoryFactory.getRepository(source.getSourceType().toUpperCase(), sourceConfig, detail.getTableName());
                try {
                    boolean exists = repository.existsTableCheck(repository.getCatalog(), null);
                    if (exists) {
                        //删除表
                        repository.executeSql(String.format("TRUNCATE TABLE %s", detail.getTableName()));
                    }
                } catch (SQLException e) {
                    log.error("数据库操作异常。");
                    throw new ServiceException("数据库操作异常。");
                }
            }
        }
        return true;
    }

    public static void main(String[] args) {
        // SOH字符的ASCII值是1
        char SOH = 1;

        char END = 5;

        // 文件路径
        String filePath = "/Users/<USER>/Desktop/output.txt";

        // 使用OutputStreamWriter和FileOutputStream来写入文件，指定使用ASCII编码
        try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.US_ASCII)) {
            // 写入SOH字符
            writer.write(SOH);


            // 继续写入其他文本内容
            writer.write("This is a test string following SOH.");
            writer.write(END);

            System.out.println("SOH character has been inserted into the text file.");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
