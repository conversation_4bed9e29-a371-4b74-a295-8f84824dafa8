package com.dcube.tran;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TaskTest {

    /*public static void main(String[] args) throws Exception {

        String tarTableName = "sample";
        String deleteSql = "delete from sample";
        String selectSql = "select orgname , custman_user from d_loan";
        String countSql = "select count(1) from d_loan";
        List<String> columnCodeList = Arrays.asList("name", "description");

        List<MemColumn> columnList = new ArrayList<>();
        for (String column : columnCodeList) {
            MemColumn c = new MemColumn();
            c.setKey(column.toLowerCase());
            c.setType(MemColumn.ColumnType.STRING);
            columnList.add(c);
        }

        SourceConfigJson resConfig = new SourceConfigJson();
        resConfig.setDriverClass("com.mysql.cj.jdbc.Driver");
        resConfig.setUrl("jdbc:mysql://************:3306/dcube");
        resConfig.setUsername("root");
        resConfig.setPassword("123456");

        AbstractRepository resRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MYSQL, resConfig, null);

        // 统计接入总数
        long total = resRepository.executeCountSql(countSql);
        // 清空目标表数据
        //tarRepository.executeSql(deleteSql);
        // 创建报告对象
        ReportDto reportDto = new ReportDto("taskId", "taskName", total);
        // 注册报告对象
        TaskReport.put(reportDto);
        // 创建读对象
        RdbmsReader reader = new RdbmsReader(resRepository, selectSql, reportDto);
        // 创建写对象
        GridWriter writer = new GridWriter(tarTableName, columnList, reportDto);
        // 创建数据处理任务
        TaskSingleContainer container = new TaskSingleContainer(1, reader, writer, null);
        // 启动数据处理任务
        container.run();


        Class.forName("org.apache.calcite.jdbc.Driver");
        Properties info = new Properties();
        info.setProperty("lex", "JAVA");
        Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
        CalciteConnection calciteConnection = connection.unwrap(CalciteConnection.class);
        SchemaPlus rootSchema = calciteConnection.getRootSchema();
        Schema schema = MemSchema.INSTANCE;

        rootSchema.add("hr", schema);
        Statement statement = calciteConnection.createStatement();

        long time2 = System.currentTimeMillis();
        log.info("----查询开始：{}", System.currentTimeMillis());
        ResultSet resultSet = statement.executeQuery("select * from hr.test LIMIT 5 OFFSET 4");
        log.info("----查询耗时：{}", (System.currentTimeMillis() - time2) / 1000);

        *//**
     * 遍历 SQL 执行结果
     *//*
        while (resultSet.next()) {
            for (int i = 1; i <= resultSet.getMetaData().getColumnCount(); i++) {
                System.out.print(resultSet.getMetaData().getColumnName(i) + ":" + resultSet.getObject(i));
                System.out.print(" | ");
            }
            System.out.println();
        }
        resultSet.close();
        statement.close();
        connection.close();
    }*/

}
