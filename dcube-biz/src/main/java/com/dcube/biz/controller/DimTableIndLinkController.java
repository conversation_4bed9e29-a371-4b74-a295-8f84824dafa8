package com.dcube.biz.controller;

import com.dcube.biz.dto.DimTableIndLinkAddDto;
import com.dcube.biz.dto.DimTableIndLinkEffectScopeUpdateDto;
import com.dcube.biz.dto.DimTableIndLinkUpdateDto;
import com.dcube.biz.service.IDimTableIndLinkEffectScopeService;
import com.dcube.biz.service.IDimTableIndLinkService;
import com.dcube.common.annotation.Log;
import com.dcube.common.annotation.RepeatSubmit;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.validation.AddGroup;
import com.dcube.common.validation.UpdateGroup;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 多维表指标链接Controller
 */
@RestController
@RequestMapping("/dimTableIndLink")
@Tag(name = "DCUBE-多维表指标链接", description = "DCUBE-多维表指标链接")
public class DimTableIndLinkController extends BaseController {

    @Autowired
    private IDimTableIndLinkService dimTableIndLinkService;

    @Autowired
    private IDimTableIndLinkEffectScopeService dimTableIndLinkEffectScopeService;

    /**
     * 新增多维表指标链接
     */
    @Log(title = "新增多维表指标链接", businessType = BusinessType.INSERT)
    @Operation(summary = "新增多维表指标链接")
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@Validated(AddGroup.class) @RequestBody DimTableIndLinkAddDto dto) {
        Long linkId = dimTableIndLinkService.addDimTableIndLink(dto);
        return AjaxResult.success(linkId);
    }

    /**
     * 根据多维表ID查询所有链接
     */
    @Operation(summary = "根据多维表ID查询所有链接")
    @GetMapping("/listByDimTableId/{dimTableId}")
    public AjaxResult listByDimTableId(@PathVariable("dimTableId") @NotNull Long dimTableId) {
        return AjaxResult.success(dimTableIndLinkService.listByDimTableId(dimTableId));
    }

    /**
     * 根据链接ID查询链接详情
     */
    @Operation(summary = "根据链接ID查询链接详情")
    @GetMapping("/detail/{linkId}")
    public AjaxResult getDetailByLinkId(@PathVariable("linkId") @NotNull Long linkId) {
        return AjaxResult.success(dimTableIndLinkService.getDetailByLinkId(linkId));
    }

    /**
     * 根据链接ID删除链接
     */
    @Log(title = "删除多维表指标链接", businessType = BusinessType.DELETE)
    @Operation(summary = "根据链接ID删除链接")
    @DeleteMapping("/{linkId}")
    public AjaxResult deleteByLinkId(@PathVariable("linkId") @NotNull Long linkId) {
        Boolean result = dimTableIndLinkService.deleteByLinkId(linkId);
        return AjaxResult.success(result);
    }

    /**
     * 根据链接ID查询生效范围
     */
    @Operation(summary = "根据链接ID查询生效范围")
    @GetMapping("/effectScope/{linkId}")
    public AjaxResult getEffectScopeByLinkId(@PathVariable("linkId") @NotNull Long linkId) {
        return AjaxResult.success(dimTableIndLinkEffectScopeService.listByLinkId(linkId));
    }

    /**
     * 修改作用范围
     */
    @Log(title = "修改多维表指标链接生效范围", businessType = BusinessType.UPDATE)
    @Operation(summary = "修改作用范围")
    @PutMapping("/effectScope")
    public AjaxResult updateEffectScope(@Validated @RequestBody DimTableIndLinkEffectScopeUpdateDto dto) {
        Boolean result = dimTableIndLinkEffectScopeService.updateEffectScope(dto);
        return AjaxResult.success(result);
    }

    /**
     * 修改多维表指标链接
     */
    @Log(title = "修改多维表指标链接", businessType = BusinessType.UPDATE)
    @Operation(summary = "修改多维表指标链接")
    @PostMapping("/update")
    @RepeatSubmit
    public AjaxResult update(@Validated(UpdateGroup.class) @RequestBody DimTableIndLinkAddDto dto) {
        return toAjax(dimTableIndLinkService.updateDimTableIndLink(dto));
    }

}
