package com.dcube.tran.plugin;

import com.dcube.biz.util.MemGridUtils;
import com.dcube.common.dto.ReportDto;
import com.dcube.grid.MemorySchema;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.element.*;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordSender;
import lombok.extern.slf4j.Slf4j;

import java.sql.Date;

@Slf4j
public class GridReader extends Reader {

    protected TableMetaData tableMetaData;

    protected ReportDto reportDto;

    protected String tableName;

    protected final long waitSeconds = 10;

    public GridReader() {
    }

    public GridReader(String tableName, ReportDto reportDto) {
        super();
        this.tableName = tableName.toLowerCase();
        this.tableMetaData = MemGridUtils.getTableMetaData(this.tableName);
        this.reportDto = reportDto;
    }

    @Override
    public void startRead(RecordSender recordSender) {
        log.info("============================================={}==============================================", "执行GridReader");
        try {
            Object[][] tableData = MemorySchema.getDatabase().getTableDataMap().get(tableName);
            reportDto.setTotalRecord((long) tableData.length);
            int i = 0;
            while (getTaskHelp().checkStatus() && i < tableData.length) {
                Object[] row = tableData[i];
                recordSender.sendToWriter(buildRecord(row), waitSeconds);
                i++;
            }
            getTaskHelp().setReaderStatus(true);
        } catch (Exception e) {
            getTaskHelp().setReaderStatus(false);
            getTaskHelp().setMsg(e.getMessage());
            log.error(e.getMessage(), e);
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, e);
        }
    }

    private com.dcube.tran.element.Record buildRecord(Object[] row) {
        int columnCount = tableMetaData.getColumnNames().size();
        com.dcube.tran.element.Record record = new DefaultRecord(columnCount);
        int i = 0;
        try {
            for (; i < columnCount; i++) {
                String columnName = tableMetaData.getColumnNames().get(i);
                Object value = row[i];
                if (String.class.equals(tableMetaData.getColumnTypes().get(i))) {
                    StringColumn stringColumn1 = new StringColumn(value == null ? null : (String) value);
                    stringColumn1.setColumnName(columnName);
                    record.addColumn(stringColumn1);
                } else if (Double.class.equals(tableMetaData.getColumnTypes().get(i))) {
                    DoubleColumn doubleColumn = new DoubleColumn(value == null ? null : (double) value);
                    doubleColumn.setColumnName(columnName);
                    record.addColumn(doubleColumn);
                } else if (Integer.class.equals(tableMetaData.getColumnTypes().get(i))) {
                    LongColumn longColumn1 = new LongColumn(value == null ? null : Long.valueOf(value.toString()));
                    longColumn1.setColumnName(columnName);
                    record.addColumn(longColumn1);
                } else if (Date.class.equals(tableMetaData.getColumnTypes().get(i))) {
                    DateColumn dateColumn = new DateColumn(value == null ? null : (Date) value);
                    dateColumn.setColumnName(columnName);
                    record.addColumn(dateColumn);
                } else if (java.util.Date.class.equals(tableMetaData.getColumnTypes().get(i))) {
                    DateColumn dateColumn = new DateColumn(value == null ? null : (java.util.Date) value);
                    dateColumn.setColumnName(columnName);
                    record.addColumn(dateColumn);
                } else {
                    throw DataTranException
                            .asDataTranException(
                                    CommonErrorCode.READ_NOT_SUPPORT,
                                    String.format(
                                            "不支持的字段类型. 字段名:[%s],  字段Java类型:[%s].",
                                            columnName,
                                            tableMetaData.getColumnTypes().get(i)));
                }
            }
        } catch (Exception e) {
            log.error("read index:{} data: {} occur exception:", i, row[i], e);
            throw new IllegalStateException(e);
        }
        return record;
    }
}