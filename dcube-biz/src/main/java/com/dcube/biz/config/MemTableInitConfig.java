package com.dcube.biz.config;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.TableDto;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.enums.TableType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2023/9/20 17:20
 * @描述 二维表初始化
 */
@Slf4j
@Configuration
public class MemTableInitConfig {

    @Autowired
    private ITableService tableService;

//    @PostConstruct
    public void init() {
        List<Table> tables = queryInitTables();
        if (CollectionUtil.isNotEmpty(tables)) {
            tables.forEach(table -> {
                log.info("init memory table:{}", table.getTableName());
                try {
                    TableVo tableVo = tableService.get(table.getId());
                    TableDto dto = new TableDto();
                    BeanUtils.copyProperties(tableVo, dto);
                    MemGridUtils.createTable(dto);
                } catch (Exception e) {
                    log.error("init memory table error:{}", table.getTableName(), e);
                }
            });
        }
    }

    private List<Table> queryInitTables() {
        LambdaQueryWrapper<Table> qw = new LambdaQueryWrapper<>();
        qw.select(Table::getId, Table::getTableName, Table::getMemTableName, Table::getType, Table::getTableMeta, Table::getType);
        qw.eq(Table::getType, TableType.TABLE.getCode());
        return tableService.list(qw);
    }
}
