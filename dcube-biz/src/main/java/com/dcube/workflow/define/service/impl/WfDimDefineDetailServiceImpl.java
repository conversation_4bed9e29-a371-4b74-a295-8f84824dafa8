package com.dcube.workflow.define.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.system.service.ISysUserService;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.define.domain.WfDimDefineDetail;
import com.dcube.workflow.define.dto.WfDimDefineDetailSaveBatchDTO;
import com.dcube.workflow.define.dto.WfDimDefineDetailSaveDTO;
import com.dcube.workflow.define.exception.WfDefineException;
import com.dcube.workflow.define.mapper.WfDimDefineDetailMapper;
import com.dcube.workflow.define.service.IWfDimDefineCentralizeService;
import com.dcube.workflow.define.service.IWfDimDefineDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WfDimDefineDetailServiceImpl extends ServiceImpl<WfDimDefineDetailMapper, WfDimDefineDetail> implements IWfDimDefineDetailService {

    @Autowired
    private IWfDimDefineCentralizeService wfDimDefineCentralizeService;
    @Autowired
    private IDimInstanceService dimInstanceService;
    @Autowired
    private ISysUserService sysUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatch(WfDimDefineDetailSaveBatchDTO wfDimDefineDetailSaveBatchDto) {
        // 删除
        this.remove(Wrappers.<WfDimDefineDetail>lambdaQuery().eq(WfDimDefineDetail::getDefineId, wfDimDefineDetailSaveBatchDto.getDefineId()));
        if (CollectionUtils.isEmpty(wfDimDefineDetailSaveBatchDto.getDimDefineDetails())) {
            return Boolean.FALSE;
        }
        wfDimDefineDetailSaveBatchDto.getDimDefineDetails().forEach(wfDimDefineDetail -> wfDimDefineDetail.setDefineId(wfDimDefineDetailSaveBatchDto.getDefineId()));
        return this.saveBatch(wfDimDefineDetailSaveBatchDto.getDimDefineDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(WfDimDefineDetailSaveDTO wfDimDefineDetailSaveDto) {
        if (StringUtils.isNotEmpty(wfDimDefineDetailSaveDto.getCentralizedDimId())) {
            DimInstance dimInstance = dimInstanceService.getById(wfDimDefineDetailSaveDto.getDimId());
            Assert.isTrue(StringUtils.equals(dimInstance.getIsLeaf(), "N"), "叶子节点不允许设置归口维度");
        }
        // todo 校验归口维度审批人
        WfDimDefineDetail wfDimDefineDetail = new WfDimDefineDetail();
        BeanUtils.copyProperties(wfDimDefineDetailSaveDto, wfDimDefineDetail);
        return this.saveOrUpdate(wfDimDefineDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCentralize(WfDimDefineCentralize wfDimDefineCentralize) {
        List<WfDimDefineCentralize> list = wfDimDefineCentralizeService.list(Wrappers.<WfDimDefineCentralize>lambdaQuery()
                .eq(WfDimDefineCentralize::getDimDefineDetailId, wfDimDefineCentralize.getDimDefineDetailId())
                .ne(Objects.nonNull(wfDimDefineCentralize.getId()), WfDimDefineCentralize::getId, wfDimDefineCentralize.getId()));
        if (CollectionUtils.isNotEmpty(list)) {
            String[] split = wfDimDefineCentralize.getCentralizeDimId().split(",");
            for (WfDimDefineCentralize wfDimDefineCentralize1 : list) {
                Set<String> set = Arrays.stream(wfDimDefineCentralize1.getCentralizeDimId().split(",")).collect(Collectors.toSet());
                for (String dimId : split) {
                    if (set.contains(dimId)) {
                        throw new WfDefineException("维度【" + dimInstanceService.getById(dimId).getDimName() + "】已在" + wfDimDefineCentralize1.getCentralizeName() + "中设置，不能重复");
                    }
                }
            }
        }
        return wfDimDefineCentralizeService.saveOrUpdate(wfDimDefineCentralize);
    }

    @Override
    public List<WfDimDefineCentralize> getCentralize(Long dimDefineDetailId) {
        List<WfDimDefineCentralize> list = wfDimDefineCentralizeService.list(Wrappers.<WfDimDefineCentralize>lambdaQuery()
                .eq(WfDimDefineCentralize::getDimDefineDetailId, dimDefineDetailId));
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> userIds = new HashSet<>();
            for (WfDimDefineCentralize wfDimDefineCentralize : list) {
                if (StringUtils.isNotEmpty(wfDimDefineCentralize.getUserId())) {
                    List<String> userIdList = Arrays.asList(wfDimDefineCentralize.getUserId().split(","));
                    wfDimDefineCentralize.setUserIds(userIdList);
                    userIds.addAll(userIdList.stream().map(Long::valueOf).collect(Collectors.toSet()));
                }
            }
            Map<Long, String> userIdMap;
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<SysUser> sysUsers = sysUserService.listByIds(userIds);
                userIdMap = StreamUtils.toMap(sysUsers, SysUser::getUserId, v -> v.getNickName() + "(" + v.getUserName() + ")");
            } else {
                userIdMap = Collections.emptyMap();
            }
            list.forEach(wfDimDefineCentralize -> {
                if (CollectionUtils.isNotEmpty(wfDimDefineCentralize.getUserIds())) {
                    wfDimDefineCentralize.setUserName(wfDimDefineCentralize.getUserIds().stream().map(v -> MapUtils.getObject(userIdMap, Long.valueOf(v))).collect(Collectors.joining(",")));
                }
            });
        }
        return list;
    }

    @Override
    public List<WfDimDefineCentralize> getCentralizes(Collection<? extends Serializable> dimDefineDetailIds) {
        if (CollectionUtils.isEmpty(dimDefineDetailIds)) {
            return Collections.emptyList();
        }
        return wfDimDefineCentralizeService.list(Wrappers.<WfDimDefineCentralize>lambdaQuery()
                .in(WfDimDefineCentralize::getDimDefineDetailId, dimDefineDetailIds));
    }

}
