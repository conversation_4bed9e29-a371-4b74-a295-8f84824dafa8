package com.dcube.workflow.runtime.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.workflow.runtime.constants.WfRtConstants;
import com.dcube.workflow.runtime.constants.enums.WfEventTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfOperateTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.domain.WfVariable;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.dcube.workflow.runtime.listener.event.WfEvent;
import com.dcube.workflow.runtime.listener.event.WfEventSource;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.service.IWfVariableService;
import com.dcube.workflow.runtime.strategy.ApproveStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

/**
 * 撤回
 */
@Service("WITHDRAW")
public class WithdrawStrategyImpl implements ApproveStrategy {

    @Autowired
    private IWfInstanceService wfInstanceService;
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private IWfVariableService wfVariableService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult approve(WfTaskDTO wfTaskDto) {
        WfTask wfTask = wfTaskService.getById(wfTaskDto.getId());
        Assert.notNull(wfTask, "未查询到任务");
        if (!StringUtils.equals(wfTask.getTaskDone(), "1")) {
            throw new WfRuntimeException("任务未处理，无法撤回");
        }
        WfInstance wfInstance = wfInstanceService.getById(wfTask.getInstanceId());
        Assert.notNull(wfInstance, "未查询到流程实例");
        if (wfInstance.getInstanceState() != WfInstanceStateEnums.RUNNING) {
            throw new WfRuntimeException("流程实例状态不为运行中，无法撤回");
        }
        wfTask.setCommentContent(wfTaskDto.getCommentContent());
        wfTask.setOperateType(WfOperateTypeEnums.WITHDRAW);
        switch (wfTask.getFlowType()) {
            case TABLE:
                handleTableTask(wfTask, wfInstance);
                break;
            case CUBE:
                handleCubeTask(wfTask);
                break;
        }

        // 发布撤回事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfTask, wfEventSource);
        wfEventSource.setCommentContent(SecurityUtils.getLoginUser().getUser().getNickName() + " 撤回");
        wfEventSource.setEventType(WfEventTypeEnums.TASK_WITHDRAW);
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));

        return AjaxResult.success("撤回成功");
    }

    private void handleTableTask(WfTask wfTask, WfInstance wfInstance) {
        String nextNodeId = wfTask.getNextNodeId();
        if (!StringUtils.equals(wfInstance.getNodeId(), nextNodeId)) {
            throw new WfRuntimeException("下一节点任务已被处理，无法撤回");
        }

        // 删除下一节点任务
        QueryWrapper<WfTask> wfTaskQueryWrapper = new QueryWrapper<>();
        wfTaskQueryWrapper.eq("INSTANCE_ID", wfTask.getInstanceId());
        wfTaskQueryWrapper.eq("NODE_ID", wfTask.getNodeId());
        wfTaskService.remove(wfTaskQueryWrapper);

        // 创建待办任务
        WfTask newTask = new WfTask();
        BeanUtils.copyProperties(wfTask, newTask);
        newTask.setTaskDone("0");
        newTask.setApproveRoleId("");
        newTask.setApproveRoleName("");
        newTask.setCommentContent("");
        newTask.setNextEdgeId("");
        newTask.setNextEdgeName("");
        newTask.setNextNodeId("");
        newTask.setNextNodeName("");
        newTask.setApproveTime(null);
        newTask.setCost(null);
        wfTaskService.createTask(newTask);

        // 更新流程实例
        UpdateWrapper<WfInstance> wfInstanceUpdateWrapper = new UpdateWrapper<>();
        wfInstanceUpdateWrapper.set("NODE_ID", wfTask.getNodeId());
        wfInstanceUpdateWrapper.set("NODE_NAME", wfTask.getNodeName());
        wfInstanceUpdateWrapper.eq("ID", wfTask.getInstanceId());
        wfInstanceUpdateWrapper.eq("NODE_ID", wfInstance.getNodeId());
        boolean update = wfInstanceService.update(wfInstanceUpdateWrapper);
        if (!update) {
            throw new WfRuntimeException("流程实例已被修改，请重试");
        }

    }

    private void handleCubeTask(WfTask wfTask) {
        //归口节点不能撤回。
        //其它节点：上级节点处于“待提交”状态。否则提示：上级节点已提交，不能撤回！
        Assert.isTrue(wfTask.getCentralizeDimDefineId() == null, "归口节点不能撤回");
        if (StringUtils.isNotEmpty(wfTask.getParentNodeId())) {
            List<WfTask> parentTaskList = wfTaskService.list(Wrappers.<WfTask>lambdaQuery()
                    .eq(WfTask::getNodeId, wfTask.getParentNodeId()));
            if (CollectionUtils.isNotEmpty(parentTaskList)) {
                Optional<WfTask> optional = parentTaskList.stream()
                        .filter(task -> task.getTaskState() == WfTaskStateEnums.PRE_SUBMIT)
                        .findAny();
                if (!optional.isPresent()) {
                    throw new WfRuntimeException("上级节点已提交，不能撤回！");
                }
            }
        }
//        List<WfTask> wfTaskList = wfTaskService.getTasksByNodeId(wfTask.getNodeId(), true);

        if (StringUtils.equals(wfTask.getUserId(), String.valueOf(SecurityUtils.getUserId()))) {
            if (wfTask.getTaskState() == WfTaskStateEnums.PRE_APPROVE) {
                // 撤回为待提交
                wfTask.setTaskState(WfTaskStateEnums.PRE_SUBMIT);
            } else {
                throw new WfRuntimeException("该任务状态不是待审批，无法撤回！");
            }
        } else if (StringUtils.equals(wfTask.getOperateUserId(), String.valueOf(SecurityUtils.getUserId()))) {
            if (wfTask.getTaskState() == WfTaskStateEnums.APPROVED) {
                // 撤回为待审批
                wfTask.setTaskState(WfTaskStateEnums.PRE_APPROVE);
            } else if (wfTask.getTaskState() == WfTaskStateEnums.RETURN) {
                // 撤回为待审批
                wfTask.setTaskState(WfTaskStateEnums.PRE_APPROVE);
            } else {
                throw new WfRuntimeException("该任务无法撤回，请联系管理员！");
            }
        } else {
            throw new WfRuntimeException("该任务无法撤回，请联系管理员！");
        }
        wfTask.setTaskDone("0");
        wfTask.setOperateUserId(String.valueOf(SecurityUtils.getUserId()));
//        wfTask.setApproveTime(new Date());
//        wfTask.setCost(DateUtil.betweenMs(wfTask.getApproveTime(), wfTask.getCreateTime()));
        wfTaskService.updateById(wfTask);
        // 流程变量
        LambdaUpdateWrapper<WfVariable> wfVariableLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        // 归口节点审批
        if (wfTask.getCentralizeDimDefineId() == null) {
            wfVariableLambdaUpdateWrapper.isNull(WfVariable::getCentralizeId);
        } else {
            wfVariableLambdaUpdateWrapper.eq(WfVariable::getCentralizeId, wfTask.getCentralizeDimDefineId());
        }
        wfVariableLambdaUpdateWrapper.eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                .eq(WfVariable::getNodeId, wfTask.getNodeId())
                .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                .set(WfVariable::getVariableVal, wfTask.getTaskState().name());
        wfVariableService.update(wfVariableLambdaUpdateWrapper);

        // 上级节点的归口审批状态修改为待处理
        WfVariable wfVariable = wfVariableService.getOne(Wrappers.<WfVariable>lambdaQuery()
                .eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                .eq(WfVariable::getNodeId, wfTask.getNodeId())
                .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.PARENT_NODE_ID)
                .last("limit 1")
        );
        if (wfVariable != null && StringUtils.isEmpty(wfVariable.getVariableVal()) && StringUtils.equals(wfVariable.getVariableVal(), "0")) {
            wfVariableService.update(Wrappers.<WfVariable>lambdaUpdate()
                    .eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                    .eq(WfVariable::getNodeId, wfVariable.getVariableVal())
                    .isNotNull(WfVariable::getCentralizeId)
                    .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                    .set(WfVariable::getVariableVal, WfTaskStateEnums.PRE_HANDLE.name())
            );
            // 归口任务重置
            wfTaskService.update(Wrappers.<WfTask>lambdaUpdate()
                    .eq(WfTask::getInstanceId, wfTask.getInstanceId())
                    .eq(WfTask::getNodeId, wfVariable.getVariableVal())
                    .isNotNull(WfTask::getCentralizeDimDefineId)
                    .set(WfTask::getTaskDone, "0")
                    .set(WfTask::getOperateUserId, "")
                    .set(WfTask::getApproveTime, null)
                    .set(WfTask::getTaskState, WfTaskStateEnums.PRE_HANDLE.name())
                    .set(WfTask::getCost, null)
            );
        }

    }

}
