package com.dcube.workflow.runtime.constants.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.dcube.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum WfTaskStateEnums {
    /**
     * 待提交
     */
    PRE_SUBMIT("0", "待提交"),
    /**
     * 待审批
     */
    PRE_APPROVE("1", "待审批"),
    /**
     * 已审批
     */
    APPROVED("2", "已审批"),
    /**
     * 已撤回
     */
    WITHDRAW("3", "已撤回"),
    /**
     * 已退回
     */
    RETURN("4", "已退回"),
    /**
     * 待处理
     */
    PRE_HANDLE("5", "待处理"),
    ;

    @EnumValue
    private final String state;

    /**
     * 中文描述
     */
    private final String cName;

    public static WfTaskStateEnums getByName(String name) {
        return Arrays.stream(WfTaskStateEnums.values())
                .filter(e -> StringUtils.equals(e.name(), name))
                .findFirst()
                .orElse(null);
    }

}
