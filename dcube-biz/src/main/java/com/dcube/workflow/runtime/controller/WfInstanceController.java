package com.dcube.workflow.runtime.controller;

import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.dto.WfGetNextEdgeDTO;
import com.dcube.workflow.runtime.dto.WfInstanceStartDTO;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import com.dcube.workflow.runtime.service.IWfTaskService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 流程运行实例Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@RestController
@RequestMapping("/workflow/runtime/instance")
@Tag(name = "DCUBE-流程运行实例", description = "DCUBE-流程运行实例")
public class WfInstanceController extends BaseController {
    @Autowired
    private IWfInstanceService wfInstanceService;
    @Autowired
    private IWfTaskService wfTaskService;

    /**
     * 查询流程运行实例列表
     */
    @Operation(summary = "查询流程运行实例列表")
    @GetMapping("/list")
    public TableDataInfo list(WfInstance wfInstance) {
        startPage();
        List<WfInstance> list = wfInstanceService.selectCubeWfInstanceList(wfInstance);
        return getDataTable(list);
    }

    /**
     * 获取流程运行实例详细信息
     */
    @Operation(summary = "获取流程运行实例详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wfInstanceService.selectCubeWfInstanceById(id));
    }

    /**
     * 新增流程运行实例
     */
    @Operation(summary = "新增流程运行实例")
    @Log(title = "流程运行实例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WfInstance wfInstance) {
        return toAjax(wfInstanceService.insertCubeWfInstance(wfInstance));
    }

    /**
     * 修改流程运行实例
     */
    @Operation(summary = "修改流程运行实例")
    @Log(title = "流程运行实例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WfInstance wfInstance) {
        return toAjax(wfInstanceService.updateCubeWfInstance(wfInstance));
    }

    /**
     * 删除流程运行实例
     */
    @Operation(summary = "删除流程运行实例")
    @Log(title = "流程运行实例", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wfInstanceService.deleteCubeWfInstanceByIds(ids));
    }

    /**
     * 获取下一个线
     */
    @Operation(summary = "获取下一个线")
    @PostMapping("/getNextEdges")
    public AjaxResult getNextEdges(@RequestBody WfGetNextEdgeDTO wfGetNextEdgeDto) {
        return AjaxResult.success(wfInstanceService.getNextEdges(wfGetNextEdgeDto.getTableId(), wfGetNextEdgeDto.getTaskId(), wfGetNextEdgeDto.getNodeId(), wfGetNextEdgeDto.getDataId()));
    }

    /**
     * 获取当前节点
     */
    @Operation(summary = "获取当前节点")
    @GetMapping("/getCurrentNode")
    public AjaxResult getCurrentNode(@RequestParam("tableId") Integer tableId,
                                     @RequestParam(value = "instanceId", required = false) Long instanceId) {
        return AjaxResult.success(wfInstanceService.getCurrentNode(tableId, instanceId));
    }

    /**
     * 获取提交信息
     */
    @Operation(summary = "获取提交信息")
    @PostMapping("/submitInfo")
    public AjaxResult submitInfo(@RequestBody WfGetNextEdgeDTO wfGetNextEdgeDto) {
        return AjaxResult.success(wfTaskService.nextEdgeInfo(wfGetNextEdgeDto.getTableId(), wfGetNextEdgeDto.getTaskId(), wfGetNextEdgeDto.getDataId(), wfGetNextEdgeDto.getFlowType()));
    }

    /**
     * 发起流程
     */
    @Operation(summary = "发起流程")
    @PostMapping("/startProcess")
    public AjaxResult startProcess(@RequestBody WfInstanceStartDTO wfInstanceStartDto) {
        return AjaxResult.success(wfInstanceService.startProcess(wfInstanceStartDto));
    }

    /**
     * 终止流程
     */
    @Operation(summary = "终止流程")
    @PostMapping("/stopProcess")
    public AjaxResult stopProcess(@RequestBody WfInstanceStartDTO wfInstanceStartDto) {
        return AjaxResult.success(wfInstanceService.stopProcess(wfInstanceStartDto));
    }


}
