package com.dcube.workflow.define.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.workflow.define.constants.enums.WfDefineFlowTypeEnums;
import com.dcube.workflow.define.constants.enums.WfDefineTypeEnums;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程定义对象 cube_wf_define
 *
 * @date 2024-02-25
 */
@Data
@TableName("cube_wf_define")
@EqualsAndHashCode(callSuper = false)
@ToString
public class WfDefine extends Model<WfDefine> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 表id
     */
    @Schema(description = "表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 流程类型（二维、多维）
     */
    @Schema(description = "流程类型（二维、多维）")
    private WfDefineFlowTypeEnums flowType;

    /**
     * 流程类型（二维、多维）
     */
    @Schema(description = "流程类型（二维、多维）")
    @TableField(exist = false)
    private String flowTypeName;

    /**
     * 流程名称
     */
    @Schema(description = "流程名称")
    private String flowName;

    /**
     * 流程状态列名称
     */
    @Schema(description = "流程状态列名称")
    private String flowStateColumnName;

    /**
     * 流程状态列编码
     */
    @Schema(description = "流程状态列编码")
    private String flowStateColumnCode;

    /**
     * 批量提交
     */
    @Schema(description = "批量提交")
    private String batchSubmit;

    /**
     * 父Id
     */
    @Schema(description = "父Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 类型（分组、流程）
     */
    @Schema(description = "类型（分组、流程）")
    private WfDefineTypeEnums type;

    /**
     * 祖级列表
     */
    @Schema(description = "祖级列表")
    private String ancestors;

    /**
     * 流程图json
     */
    @Schema(description = "流程图json")
    private String flowchartJson;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 表名称
     */
    @Schema(description = "表名称")
    @TableField(exist = false)
    private String tableName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;
}
