package com.dcube.workflow.define.vo;

import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
public class WfDimDefineDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defineId;

    /**
     * 维度id
     */
    @Schema(description = "维度id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimId;

    /**
     * 维度名称
     */
    @Schema(description = "维度名称")
    private String dimName;

    /**
     * 审批人id
     */
    @Schema(description = "审批人id")
    private String userId;

    /**
     * 审批人ids
     */
    @Schema(description = "审批人ids")
    private List<String> userIds = Collections.emptyList();

    /**
     * 归口维度id
     */
    @Schema(description = "归口维度id")
    private String centralizedDimId = "";

    /**
     * 归口维度名称
     */
    @Schema(description = "归口维度名称")
    private String centralizedDimName;

    /**
     * 审批人名称
     */
    @Schema(description = "审批人名称")
    private String userName;

    @Schema(description = "祖级列表")
    private String ancestors;

    /**
     * 上级维度成员ID
     */
    @Schema(description = "上级维度成员ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 是否叶子
     */
    @Schema(description = "是否叶子")
    private String isLeaf;

    /**
     * 归口维度
     */
    @Schema(description = "归口维度")
    private List<WfDimDefineCentralize> centralizeList;
}
