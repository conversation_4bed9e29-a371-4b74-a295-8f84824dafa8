package com.dcube.workflow.define.utils;

import com.dcube.common.utils.StringUtils;
import com.dcube.workflow.define.constants.enums.FlowNodeTypeEnums;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class FlowChartUtils {
    private FlowChartUtils() {

    }

    public static FlowChart.NodeDTO getStartNode(FlowChart flowChart) {
        if (flowChart == null) {
            return null;
        }

        Optional<FlowChart.NodeDTO> optional = flowChart.getNodes().stream().filter(v -> FlowNodeTypeEnums.START == v.getType()).findFirst();
        return optional.orElse(null);
    }

    public static List<FlowChart.NodeDTO> getStartNodes(FlowChart flowChart) {
        if (flowChart == null) {
            return null;
        }

        return flowChart.getNodes().stream().filter(v -> FlowNodeTypeEnums.START == v.getType()).collect(Collectors.toList());
    }

    public static List<FlowChart.NodeDTO> getNextNodes(FlowChart flowChart, String nodeId) {
        if (flowChart == null || StringUtils.isEmpty(nodeId)) {
            return Collections.emptyList();
        }

        Set<String> targetNodeIds = flowChart.getEdges().stream().filter(v -> StringUtils.equals(nodeId, v.getSourceNodeId()))
                .map(FlowChart.EdgeDTO::getTargetNodeId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(targetNodeIds)) {
            return flowChart.getNodes().stream().filter(v -> targetNodeIds.contains(v.getId())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public static List<FlowChart.NodeDTO> getNextNodes(FlowChart flowChart, FlowChart.NodeDTO node) {
        if (flowChart == null || node == null) {
            return Collections.emptyList();
        }

        Set<String> targetNodeIds = flowChart.getEdges().stream().filter(v -> StringUtils.equals(node.getId(), v.getSourceNodeId()))
                .map(FlowChart.EdgeDTO::getTargetNodeId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(targetNodeIds)) {
            return flowChart.getNodes().stream().filter(v -> targetNodeIds.contains(v.getId())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public static FlowChart.NodeDTO getNodeById(FlowChart flowChart, String nodeId) {
        if (flowChart == null || StringUtils.isEmpty(nodeId)) {
            return null;
        }

        Optional<FlowChart.NodeDTO> optional = flowChart.getNodes().stream().filter(v -> StringUtils.equals(v.getId(), nodeId)).findFirst();
        return optional.orElse(null);
    }

    public static FlowChart.EdgeDTO getEdgeById(FlowChart flowChart, String edgeId) {
        if (flowChart == null || StringUtils.isEmpty(edgeId)) {
            return null;
        }

        Optional<FlowChart.EdgeDTO> optional = flowChart.getEdges().stream().filter(v -> StringUtils.equals(v.getId(), edgeId)).findFirst();
        return optional.orElse(null);
    }

    public static List<FlowChart.EdgeDTO> getEdgesBySourceId(FlowChart flowChart, String sourceId) {
        if (flowChart == null || StringUtils.isEmpty(sourceId)) {
            return null;
        }

        return flowChart.getEdges()
                .stream()
                .filter(v -> StringUtils.equals(v.getSourceNodeId(), sourceId))
                .collect(Collectors.toList());
    }

    public static FlowChart.NodeDTO getTargetNodeByEdgeId(FlowChart flowChart, String edgeId) {
        if (flowChart == null || StringUtils.isEmpty(edgeId)) {
            return null;
        }
        FlowChart.EdgeDTO edge = getEdgeById(flowChart, edgeId);
        if (edge == null) {
            return null;
        }
        Optional<FlowChart.NodeDTO> optional = flowChart.getNodes().stream().filter(v -> StringUtils.equals(v.getId(), edge.getTargetNodeId())).findFirst();
        return optional.orElse(null);
    }

    public static Map<String, FlowChart.NodeDTO> getTargetNodesByEdges(FlowChart flowChart, List<FlowChart.EdgeDTO> edgeS) {
        if (flowChart == null || CollectionUtils.isEmpty(edgeS)) {
            return Collections.emptyMap();
        }

        Map<String, FlowChart.NodeDTO> map = Maps.newHashMapWithExpectedSize(edgeS.size());
        for (FlowChart.EdgeDTO edge : edgeS) {
            for (FlowChart.NodeDTO nodeDTO : flowChart.getNodes()) {
                if (StringUtils.equals(edge.getTargetNodeId(), nodeDTO.getId())) {
                    map.put(edge.getId(), nodeDTO);
                }
            }
        }
        return map;
    }

    public static String getEdgeNameById(FlowChart flowChart, String nextEdgeId) {
        FlowChart.EdgeDTO edge = getEdgeById(flowChart, nextEdgeId);
        if (edge != null && edge.getProperties() != null) {
            return edge.getProperties().getText();
        }
        return "";
    }
}
