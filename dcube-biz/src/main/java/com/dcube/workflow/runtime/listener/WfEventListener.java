package com.dcube.workflow.runtime.listener;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dcube.biz.dto.RowDataDto;
import com.dcube.biz.service.ITableService;
import com.dcube.grid.MemorySchema;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.dcube.workflow.runtime.domain.WfHistory;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.listener.event.WfEvent;
import com.dcube.workflow.runtime.listener.event.WfEventSource;
import com.dcube.workflow.runtime.service.IWfHistoryService;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class WfEventListener {
    @Autowired
    private IWfHistoryService wfHistoryService;
    @Autowired
    private IWfInstanceService wfInstanceService;
    @Autowired
    private ITableService tableService;

    @EventListener
    public void listenWfEvent(WfEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    doListenWfEvent(event);
                }
            });
        } else {
            doListenWfEvent(event);
        }

    }

    private void doListenWfEvent(WfEvent event) {
        WfEventSource eventSource = event.getSource();
        switch (eventSource.getEventType()) {
            case START_PROCESS:
                handleStartProcessEvent(eventSource);
                break;
            case TASK_HANDLE:
                handleTaskHandleEvent(eventSource);
                break;
            case TASK_CREATED:
                break;
            case PROCESS_END:
                handleProcessEndEvent(eventSource);
                break;
            case TASK_WITHDRAW:
                handleTaskWithdraw(eventSource);
                break;
        }
    }


    private void handleStartProcessEvent(WfEventSource eventSource) {
        updateTableRowData(eventSource);
    }

    private void updateTableRowData(WfEventSource eventSource) {
        switch (eventSource.getFlowType()) {
            case TABLE:
                RowDataDto rowDataDto = new RowDataDto();
                rowDataDto.setMemTableName(eventSource.getMemTableName());
                rowDataDto.setRows(eventSource.getRows());
                //更新数据
                List<Object[]> rows = tableService.parseDataColl(rowDataDto);
                MemorySchema.getDatabase().updateData(eventSource.getMemTableName(), eventSource.getTableLevel(), rows);
                break;
            case CUBE:
                break;
        }

    }


    private void handleTaskHandleEvent(WfEventSource eventSource) {
        insertHistory(eventSource);
    }

    private void insertHistory(WfEventSource eventSource) {
        WfHistory wfHistory = new WfHistory();
        wfHistory.setInstanceId(eventSource.getInstanceId());
        wfHistory.setCommentContent(eventSource.getCommentContent());
        wfHistory.setDataId(eventSource.getDataId());
        wfHistory.setDefineId(eventSource.getDefineId());
        wfHistory.setNodeId(eventSource.getNodeId());
        wfHistory.setNodeName(eventSource.getNodeName());
        wfHistory.setTableId(eventSource.getTableId());
        wfHistory.setUserId(eventSource.getUserId());
        wfHistory.setUserName(eventSource.getUserName());
        wfHistory.setTaskId(eventSource.getId());
        wfHistory.setNextEdgeId(eventSource.getNextEdgeId());
        wfHistory.setNextEdgeName(eventSource.getNextEdgeName());
        wfHistory.setNextNodeId(eventSource.getNextNodeId());
        wfHistory.setNextNodeName(eventSource.getNextNodeName());
        wfHistory.setOperateType(eventSource.getOperateType());
        wfHistory.setCentralizeDimDefineId(eventSource.getCentralizeDimDefineId());
        wfHistoryService.save(wfHistory);
    }

    private void handleProcessEndEvent(WfEventSource eventSource) {
        UpdateWrapper<WfInstance> wrapper = new UpdateWrapper<>();
        wrapper.eq("id", eventSource.getInstanceId());
        wrapper.set("instance_state", WfInstanceStateEnums.COMPLETE);
        wrapper.set("end_time", new Date());
        if (eventSource.getFlowType() == WfInstanceTypeEnums.TABLE) {
            wrapper.set("node_id", eventSource.getNextNodeId());
            wrapper.set("node_name", eventSource.getNextNodeName());
        }
        wfInstanceService.update(wrapper);

        updateTableRowData(eventSource);
    }

    private void handleTaskWithdraw(WfEventSource eventSource) {
        insertHistory(eventSource);
    }

}
