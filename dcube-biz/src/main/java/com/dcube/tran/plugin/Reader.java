package com.dcube.tran.plugin;

import com.dcube.tran.base.BaseObject;
import com.dcube.tran.exchanger.RecordSender;
import com.dcube.tran.task.TaskHelp;

public abstract class Reader extends BaseObject {

    private int fetchSize = 1024 * 5;

    private volatile TaskHelp taskHelp;

    public abstract void startRead(RecordSender recordSender);

    public int getFetchSize() {
        return fetchSize;
    }

    public void setFetchSize(int fetchSize) {
        this.fetchSize = fetchSize;
    }

    public TaskHelp getTaskHelp() {
        return taskHelp;
    }

    public void setTaskHelp(TaskHelp taskHelp) {
        this.taskHelp = taskHelp;
    }
}
