package com.dcube.tran.execution;

import com.dcube.tran.element.Column;
import com.dcube.tran.element.LongColumn;
import com.dcube.tran.element.Record;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GroupDistinctTranExecution extends AbstractTranExecution {

    public GroupDistinctTranExecution(Map<String, Object> tranParasMap) {
        super(tranParasMap);
        assert null != tranParasMap;
        this.mainKey = (String) tranParasMap.get("mainKey");
    }

    private final String mainKey;

    @Setter
    @Getter
    private int index;

    @Override
    public List<Record> doTran(Record record) {
        List<Record> records = new ArrayList<>();
        Column mainKeyColumn = new LongColumn();
        mainKeyColumn.setColumnName(mainKey);
        mainKeyColumn.setColumn((long) index++);
        record.addColumn(mainKeyColumn);
        records.add(record);
        return records;
    }
}
