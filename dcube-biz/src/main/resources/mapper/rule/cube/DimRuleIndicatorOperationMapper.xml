<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.rule.cube.mapper.DimRuleIndicatorOperationMapper">

    <resultMap id="BaseResultMap" type="com.dcube.rule.cube.domain.DimRuleIndicatorOperation">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="dimRuleId" column="dim_rule_id" jdbcType="BIGINT"/>
        <result property="dimDirectoryId" column="dim_directory_id" jdbcType="BIGINT"/>
        <result property="dimDirectoryName" column="dim_directory_name" jdbcType="VARCHAR"/>
        <result property="effectScope" column="effect_scope" jdbcType="VARCHAR"/>
        <result property="indId" column="ind_id" jdbcType="BIGINT"/>
        <result property="indName" column="ind_name" jdbcType="VARCHAR"/>
        <result property="ruleExpression" column="rule_expression" jdbcType="VARCHAR"/>
        <result property="indicatorOperationType" column="indicator_operation_type" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,dim_rule_id,dim_directory_id,
        dim_directory_name,effect_scope,ind_id,
        ind_name,rule_expression,indicator_operation_type,
        create_by,create_time,update_by,
        update_time,del_flag
    </sql>
</mapper>
