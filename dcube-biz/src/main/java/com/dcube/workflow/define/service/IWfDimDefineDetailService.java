package com.dcube.workflow.define.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.define.domain.WfDimDefineDetail;
import com.dcube.workflow.define.dto.WfDimDefineDetailSaveBatchDTO;
import com.dcube.workflow.define.dto.WfDimDefineDetailSaveDTO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface IWfDimDefineDetailService extends IService<WfDimDefineDetail> {

    Boolean saveBatch(WfDimDefineDetailSaveBatchDTO wfDimDefineDetailSaveBatchDto);

    Boolean save(WfDimDefineDetailSaveDTO wfDimDefineDetailSaveDto);

    Boolean saveCentralize(WfDimDefineCentralize wfDimDefineCentralize);

    List<WfDimDefineCentralize> getCentralize(Long dimDefineDetailId);

    List<WfDimDefineCentralize> getCentralizes(Collection<? extends Serializable> dimDefineDetailIds);
}
