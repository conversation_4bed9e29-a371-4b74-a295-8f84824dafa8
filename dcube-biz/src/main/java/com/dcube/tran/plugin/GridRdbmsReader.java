package com.dcube.tran.plugin;

import com.dcube.biz.util.JdbcUtils;
import com.dcube.common.dto.ReportDto;
import com.dcube.tran.element.Record;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordSender;
import com.dcube.tran.store.repository.AbstractRepository;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;

@Slf4j
public class GridRdbmsReader extends RdbmsReader {

    private GridRdbmsReader() {
        super();
    }

    public GridRdbmsReader(AbstractRepository repository, String querySql, ReportDto reportDto) {
        super(repository, querySql, reportDto);
    }

    @Override
    public void startRead(RecordSender recordSender) {
        log.info("============================================={}==============================================", "执行startRead");

        Connection conn = null;
        int columnNumber;
        ResultSet rs = null;
        try {
            conn = JdbcUtils.getGridConnection();
            long start = System.currentTimeMillis();
            rs = this.query(conn, querySql, this.getFetchSize());
            log.info("sql {} spent {} ms", querySql, System.currentTimeMillis() - start);

            ResultSetMetaData metaData = rs.getMetaData();
            columnNumber = metaData.getColumnCount();

            while (getTaskHelp().checkStatus() && rs.next()) {
                this.transportOneRecord(recordSender, rs, metaData, columnNumber);
            }
            getTaskHelp().setReaderStatus(true);
        } catch (Exception e) {
            getTaskHelp().setReaderStatus(false);
            getTaskHelp().setMsg(e.getMessage());
            log.error(e.getMessage(), e);
            throw DataTranException.asDataTranException(CommonErrorCode.READ_SQL_QUERY, String.format("执行的SQL为[%s]", this.querySql));
        } finally {
            JdbcUtils.close(conn, null, rs);
        }
    }

    @Override
    protected Record transportOneRecord(RecordSender recordSender, ResultSet rs, ResultSetMetaData metaData, int columnNumber) {
        Record record = buildRecord(rs, metaData, columnNumber);
        recordSender.sendToWriter(record);
        return record;
    }
}