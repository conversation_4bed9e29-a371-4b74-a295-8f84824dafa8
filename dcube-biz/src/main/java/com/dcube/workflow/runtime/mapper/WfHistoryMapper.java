package com.dcube.workflow.runtime.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcube.workflow.runtime.domain.WfHistory;
import com.dcube.workflow.runtime.vo.WfHistoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程历史任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface WfHistoryMapper extends BaseMapper<WfHistory> {
    /**
     * 查询流程历史任务
     *
     * @param id 流程历史任务主键
     * @return 流程历史任务
     */
    WfHistory selectCubeWfHistoryById(Long id);

    /**
     * 查询流程历史任务列表
     *
     * @param wfHistory 流程历史任务
     * @return 流程历史任务集合
     */
    List<WfHistory> selectCubeWfHistoryList(WfHistory wfHistory);

    /**
     * 新增流程历史任务
     *
     * @param wfHistory 流程历史任务
     * @return 结果
     */
    int insertCubeWfHistory(WfHistory wfHistory);

    /**
     * 修改流程历史任务
     *
     * @param wfHistory 流程历史任务
     * @return 结果
     */
    int updateCubeWfHistory(WfHistory wfHistory);

    /**
     * 删除流程历史任务
     *
     * @param id 流程历史任务主键
     * @return 结果
     */
    int deleteCubeWfHistoryById(Long id);

    /**
     * 批量删除流程历史任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCubeWfHistoryByIds(Long[] ids);

    List<WfHistoryVO> getByInstanceId(@Param("instanceId") Long instanceId);
}
