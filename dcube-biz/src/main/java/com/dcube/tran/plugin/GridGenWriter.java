package com.dcube.tran.plugin;

import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.element.Column;
import com.dcube.tran.element.DefaultRecord;
import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordReceiver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class GridGenWriter extends GridWriter {

    protected static final int BATCH_SIZE = 1024 * 5;

    protected TableMetaData tableMetaData;

    protected ReportDto reportDto;

    protected String tableName;

    public GridGenWriter() {

    }

    public GridGenWriter(String tableName, TableMetaData tableMetaData, ReportDto reportDto) {
        super();
        this.tableName = tableName;
        this.tableMetaData = tableMetaData;
        this.reportDto = reportDto;
    }

    @Override
    public void startWrite(RecordReceiver recordReceiver) {
        List<Record> writeBuffer = new ArrayList<>(BATCH_SIZE);
        List<Object[]> rowDataList = new ArrayList<>(this.reportDto.getTotalRecord().intValue());
        //MemorySchema.getDatabase().createTable(tableMetaData);
        List<String> columnNames = tableMetaData.getColumnNames();
        List<Class<?>> columnTypes = tableMetaData.getColumnTypes();
        List<String> columnNameComments = tableMetaData.getColumnNameComments();
        int rowSize = columnTypes.size();
        try {
            Record record;
            while (getTaskHelp().checkStatus()) {
                record = recordReceiver.getFromReader(10);
                if (record != null) {
                    if (record instanceof TerminateRecord) {
                        break;
                    }
                    if (record != DefaultRecord.EMPTY_RECORD) {
                        Object[] row = new Object[rowSize];
                        for (int i = 0, size = columnNames.size(); i < size; i++) {
                            String columnName = columnNames.get(i);
                            boolean match = false;
                            for (Column column : record.getColumnList()) {
                                if (StringUtils.equalsIgnoreCase(columnName, column.getColumnName())) {
                                    row[i] = getValue(columnNameComments.get(i), columnTypes.get(i), column);
                                    match = true;
                                    break;
                                }
                            }
                            if (!match) {
                                row[i] = null;
                            }
                        }
                        rowDataList.add(row);
                    }
                    writeBuffer.add(record);
                    if (writeBuffer.size() >= BATCH_SIZE) {
                        // 记录报告进度
                        ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                        writeBuffer.clear();
                    }
                }
            }
            if (getTaskHelp().checkStatus() && !writeBuffer.isEmpty()) {
                // 记录报告进度
                ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                writeBuffer.clear();
            }
            if (!writeBuffer.isEmpty()) {
                log.error("❕有数据未写入，数据条数：{}，任务状态：{}", writeBuffer.size(), getTaskHelp().checkStatus());
                ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                writeBuffer.clear();
            }
            getTaskHelp().setWriterStatus(true);
            if (CollectionUtils.isNotEmpty(rowDataList)) {
                MemorySchema.getDatabase().insertData(tableName, rowDataList);
            }
        } catch (Exception e) {
            getTaskHelp().setWriterStatus(false);
            getTaskHelp().setMsg(e.getLocalizedMessage());
            log.error("{} PreWriter is error.", CommonErrorCode.RUNTIME_ERROR, e);
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "PreWriter is error.", e);
        } finally {
            writeBuffer.clear();
            TaskReport.finished(this.reportDto);
        }
    }

}