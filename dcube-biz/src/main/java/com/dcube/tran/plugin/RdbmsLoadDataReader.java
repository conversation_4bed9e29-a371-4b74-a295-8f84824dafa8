package com.dcube.tran.plugin;

import com.dcube.biz.dto.TableDataLoadConfigDTO;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.tran.element.DefaultRecord;
import com.dcube.tran.element.Record;
import com.dcube.tran.store.repository.AbstractRepository;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class RdbmsLoadDataReader extends RdbmsReader {

    private final Map<String, String> tableDataLoadConfigMap;

    public RdbmsLoadDataReader(AbstractRepository repository, String querySql, ReportDto reportDto, List<TableDataLoadConfigDTO> tableDataLoadConfigs) {
        this.repository = repository;
        this.querySql = querySql;
        this.reportDto = reportDto;
        if (CollectionUtils.isEmpty(tableDataLoadConfigs)) {
            this.tableDataLoadConfigMap = Collections.emptyMap();
        } else {
            this.tableDataLoadConfigMap = Maps.newHashMapWithExpectedSize(tableDataLoadConfigs.size());
            for (TableDataLoadConfigDTO tableDataLoadConfigDTO : tableDataLoadConfigs) {
                if (StringUtils.isNotEmpty(tableDataLoadConfigDTO.getViewColumnCode())) {
                    this.tableDataLoadConfigMap.put(tableDataLoadConfigDTO.getViewColumnCode(), tableDataLoadConfigDTO.getTableColumnCode());
                }
            }
        }
    }

    protected Record buildRecord(ResultSet rs, ResultSetMetaData metaData, int columnCount) {
        Record record = new DefaultRecord(columnCount);
        try {
            for (int i = 1; i <= columnCount; i++) {
                String columnName = MapUtils.getString(this.tableDataLoadConfigMap, metaData.getColumnName(i));
                if (StringUtils.isEmpty(columnName)) {
                    continue;
                }
                // 根据加载数据配置进行映射
                buildRecord(rs, metaData, i, columnName, record);
            }
        } catch (SQLException e) {
            log.error("read data {} occur exception:", record, e);
            throw new ServiceException("构造数据记录时出现异常！");
        }
        return record;
    }

}