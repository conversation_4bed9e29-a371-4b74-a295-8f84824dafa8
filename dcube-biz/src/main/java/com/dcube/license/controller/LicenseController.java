package com.dcube.license.controller;

import com.dcube.common.annotation.Anonymous;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.framework.license.service.AbstractServerInfos;
import com.dcube.framework.license.service.impl.LinuxServerInfos;
import com.dcube.framework.license.vo.LicenseCheckVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * @创建人 zhouhx
 * @创建时间 2025/3/31 11:35
 * @描述
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/license")
@Tag(name = "license认证", description = "license认证")
public class LicenseController {

    @Operation(summary = "获取Mac机器码")
    @PostMapping(value = "/getMacAddress")
    public AjaxResult getMacAddress() {
        AbstractServerInfos server = new LinuxServerInfos();
        LicenseCheckVo licenseCheck = server.getServerInfos();
        Set<String> macAddress = licenseCheck.getMacAddress();
        log.info("Mac地址为:{}", macAddress);
        return AjaxResult.success(macAddress);
    }
}
