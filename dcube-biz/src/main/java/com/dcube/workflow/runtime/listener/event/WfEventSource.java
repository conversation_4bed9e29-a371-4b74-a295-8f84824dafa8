package com.dcube.workflow.runtime.listener.event;

import com.dcube.biz.dto.RowDto;
import com.dcube.workflow.runtime.constants.enums.WfEventTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfOperateTypeEnums;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 流程事件
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@EqualsAndHashCode
@ToString
public class WfEventSource implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long instanceId;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defineId;

    /**
     * 流程定义名称
     */
    @Schema(description = "流程定义名称")
    private String defineName;

    /**
     * 流程状态列编码
     */
    @Schema(description = "流程状态列编码")
    private String dataId;

    /**
     * 是否完成
     */
    @Schema(description = "是否完成")
    private String taskDone;

    /**
     * 耗时
     */
    @Schema(description = "耗时")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long cost;

    /**
     * 制单人
     */
    @Schema(description = "制单人")
    private String processStarter;

    /**
     * 表id
     */
    @Schema(description = "表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 表名
     */
    @Schema(description = "表名")
    private String tableName;

    /**
     * 所属节点id
     */
    @Schema(description = "所属节点id")
    private String nodeId;

    /**
     * 所属节点名称
     */
    @Schema(description = "所属节点名称")
    private String nodeName;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String userId;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String userName;

    /**
     * 审批意见内容
     */
    @Schema(description = "审批意见内容")
    private String commentContent;

    /**
     * 审批事件类型
     */
    @Schema(description = "审批事件类型")
    private WfEventTypeEnums eventType;

    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 审批角色id
     */
    @Schema(description = "审批角色id")
    private String approveRoleId;

    /**
     * 审批角色名称
     */
    @Schema(description = "审批角色名称")
    private String approveRoleName;

    /**
     * 提交路径id
     */
    @Schema(description = "提交路径id")
    private String nextEdgeId;

    /**
     * 提交路径名称
     */
    @Schema(description = "提交路径名称")
    private String nextEdgeName;

    /**
     * 下一节点id
     */
    @Schema(description = "下一节点id")
    private String nextNodeId;

    /**
     * 下一节点名称
     */
    @Schema(description = "下一节点名称")
    private String nextNodeName;

    /**
     * 子表标记
     */
    @Schema(description = "子表标记")
    private String tableChildFlag;

    /**
     * 表层级
     */
    @Schema(description = "表层级")
    private Integer tableLevel;

    @Schema(description = "内存数据库表名")
    private String memTableName;

    @Schema(description = "字段集合和字段值")
    private List<RowDto> rows;

    @Schema(description = "是否退回给发起人")
    private boolean return2Starter;

    /**
     * 流程实例类型
     */
    @Schema(description = "流程实例类型")
    private WfInstanceTypeEnums flowType = WfInstanceTypeEnums.TABLE;

    /**
     * 任务操作类型
     */
    @Schema(description = "任务操作类型")
    private WfOperateTypeEnums operateType;

    /**
     * 归口维度流程定义id
     */
    @Schema(description = "归口维度流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long centralizeDimDefineId;

}
