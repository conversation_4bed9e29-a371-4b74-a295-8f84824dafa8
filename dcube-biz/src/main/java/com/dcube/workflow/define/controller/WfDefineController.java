package com.dcube.workflow.define.controller;

import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import com.dcube.workflow.define.domain.WfDefine;
import com.dcube.workflow.define.service.IWfDefineService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程定义Controller
 *
 * @date 2024-02-25
 */
@Tag(name = "DCUBE-流程定义", description = "DCUBE-流程定义")
@RestController
@RequestMapping("/workflow/define")
@PreAuthorize("@ss.hasPermi('approve-process:list')")
public class WfDefineController extends BaseController {
    @Autowired
    private IWfDefineService cubeWfDefineService;

    /**
     * 查询流程定义列表
     */
    @Operation(summary = "查询流程定义列表")
    @GetMapping("/list")
    public AjaxResult list(WfDefine wfDefine) {
        List<WfDefine> list = cubeWfDefineService.selectCubeWfDefineList(wfDefine);
        list.forEach(v -> {
            if (v.getFlowType() != null) {
                v.setFlowTypeName(v.getFlowType().getDesc());
            } else {
                v.setFlowTypeName("-");
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 获取流程定义详细信息
     */
    @Operation(summary = "获取流程定义详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(cubeWfDefineService.selectCubeWfDefineById(id));
    }

    /**
     * 新增流程定义
     */
    @Operation(summary = "新增流程定义")
    @Log(title = "新增流程定义", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WfDefine wfDefine) {
        cubeWfDefineService.insertCubeWfDefine(wfDefine);
        return AjaxResult.success(wfDefine);
    }

    /**
     * 修改流程定义
     */
    @Operation(summary = "修改流程定义")
    @Log(title = "修改流程定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WfDefine wfDefine) {
        return toAjax(cubeWfDefineService.updateCubeWfDefine(wfDefine));
    }

    /**
     * 删除流程定义
     */
    @Operation(summary = "删除流程定义")
    @Log(title = "删除流程定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(cubeWfDefineService.deleteCubeWfDefineByIds(ids));
    }

    /**
     * 获取2d表格列表
     */
    @Operation(summary = "获取2d表格列表")
    @GetMapping("/get2dTable")
    public AjaxResult get2dTable(TableListQuery query) {
        return AjaxResult.success(cubeWfDefineService.get2dTable(query));
    }

    /**
     * 获取2d表格列表列
     */
    @Operation(summary = "获取2d表格列表列")
    @GetMapping("/get2dTableMeta")
    public AjaxResult get2dTableMeta(@RequestParam("tableId") Integer tableId) {
        return AjaxResult.success(cubeWfDefineService.get2dTableMeta(tableId));
    }

    /**
     * 获取单位列表列
     */
    @Operation(summary = "获取单位列表列")
    @GetMapping("/getUnitTableMeta")
    public AjaxResult getUnitTableMeta(@Parameter(description = "流程定义id") @RequestParam("id") Integer id) {
        return AjaxResult.success(cubeWfDefineService.getUnitTableMeta(id));
    }

    /**
     * 获取开始节点
     */
    @Operation(summary = "获取开始节点")
    @GetMapping("/getStartNode")
    public AjaxResult getStartNode(@RequestParam("defineId") Long defineId) {
        return AjaxResult.success(cubeWfDefineService.getStartNode(defineId));
    }

    /**
     * 获取下一节点
     */
    @Operation(summary = "获取下一节点")
    @GetMapping("/getNextNodes")
    public AjaxResult getNextNodes(@RequestParam("defineId") Long defineId, @RequestParam(value = "nodeId", required = false) String nodeId) {
        return AjaxResult.success(cubeWfDefineService.getNextNodes(defineId, nodeId));
    }

    /**
     * 获取多维表格列表
     */
    @Operation(summary = "获取多维表格列表")
    @GetMapping("/getCubeTableList")
    public AjaxResult getCubeTableList(DimTableListQuery query) {
        return AjaxResult.success(cubeWfDefineService.getCubeTableList(query));
    }

    /**
     * 获取多维表审批维度
     */
    @Operation(summary = "多维表审批维度")
    @GetMapping("/getDimListByDimTableId")
    public AjaxResult getDimListByDimTableId(@RequestParam("dimTableId") Integer dimTableId) {
        return AjaxResult.success(cubeWfDefineService.getDimListByDimTableId(dimTableId));
    }

    /**
     * 查询多维流程定义详情
     */
    @Operation(summary = "查询多维流程定义详情")
    @GetMapping("/getByDefineId")
    public AjaxResult getByDefineId(@RequestParam("defineId") Long defineId, @RequestParam(value = "dimName", required = false) String dimName) {
        return AjaxResult.success(cubeWfDefineService.getByDefineId(defineId, dimName));
    }

}
