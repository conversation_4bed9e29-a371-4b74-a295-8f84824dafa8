package com.dcube.workflow.define.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
public class WfDimDefineDetailSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defineId;

    /**
     * 维度id
     */
    @Schema(description = "维度id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimId;

    /**
     * 审批人id
     */
    @Schema(description = "审批人id")
    private String userId;

    /**
     * 归口维度id
     */
    @Schema(description = "归口维度id")
    private String centralizedDimId;


}
