package com.dcube.workflow.runtime.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.workflow.runtime.constants.enums.WfOperateTypeEnums;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程历史任务对象 cube_wf_history
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@TableName("cube_wf_history")
@EqualsAndHashCode(callSuper = false)
@ToString
public class WfHistory extends Model<WfHistory> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 排序的方向desc或者asc
     */
    @TableField(exist = false)
    @JsonIgnore
    @JSONField(serialize = false)
    @Schema(description = "升序asc，降序desc（默认）")
    private String isAsc = "desc";

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long instanceId;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defineId;

    /**
     * 表id
     */
    @Schema(description = "表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 任务id
     */
    @Schema(description = "任务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 流程状态列编码
     */
    @Schema(description = "流程状态列编码")
    private String dataId;

    /**
     * 审批意见内容
     */
    @Schema(description = "审批意见内容")
    private String commentContent;

    /**
     * 所属节点id
     */
    @Schema(description = "所属节点id")
    private String nodeId;

    /**
     * 所属节点名称
     */
    @Schema(description = "所属节点名称")
    private String nodeName;

    /**
     * 审批人id
     */
    @Schema(description = "审批人id")
    private String userId;

    /**
     * 审批人名称
     */
    @Schema(description = "审批人名称")
    private String userName;
    /**
     * 提交路径id
     */
    @Schema(description = "提交路径id")
    private String nextEdgeId;
    /**
     * 提交路径名称
     */
    @Schema(description = "提交路径名称")
    private String nextEdgeName;
    /**
     * 下一节点id
     */
    @Schema(description = "下一节点id")
    private String nextNodeId;
    /**
     * 下一节点名称
     */
    @Schema(description = "下一节点名称")
    private String nextNodeName;
    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;

    /**
     * 任务操作类型
     */
    @Schema(description = "任务操作类型")
    private WfOperateTypeEnums operateType;

    /**
     * 任务操作类型名称
     */
    @Schema(description = "任务操作类型名称")
    private String operateTypeName;
    /**
     * 归口维度流程定义id
     */
    @Schema(description = "归口维度流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long centralizeDimDefineId;

}
