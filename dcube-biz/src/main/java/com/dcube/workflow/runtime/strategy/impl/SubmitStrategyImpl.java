package com.dcube.workflow.runtime.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.workflow.runtime.constants.WfRtConstants;
import com.dcube.workflow.runtime.constants.enums.WfEventTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfOperateTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.domain.WfVariable;
import com.dcube.workflow.runtime.dto.WfInstanceStartDTO;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.dcube.workflow.runtime.listener.event.WfEvent;
import com.dcube.workflow.runtime.listener.event.WfEventSource;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.service.IWfVariableService;
import com.dcube.workflow.runtime.strategy.ApproveStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * 提交
 */
@Service("SUBMIT")
public class SubmitStrategyImpl implements ApproveStrategy {

    @Autowired
    private IWfInstanceService wfInstanceService;
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private IWfVariableService wfVariableService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult approve(WfTaskDTO wfTaskDto) {
        switch (wfTaskDto.getFlowType()) {
            case TABLE:
                WfInstanceStartDTO wfInstanceStartDto = new WfInstanceStartDTO();
                BeanUtils.copyProperties(wfTaskDto, wfInstanceStartDto);
                wfInstanceStartDto.setTableId(Math.toIntExact(wfTaskDto.getTableId()));
                wfInstanceService.startProcess(wfInstanceStartDto);
                break;
            case CUBE:
                //所有节点：没有下级节点，或者“所有下级节点”状态都是“已审批”。否则提示：下级有不是已审批状态的节点，不能向上提交！
                Assert.notNull(wfTaskDto.getId(), "提交的任务id不能为空");
                WfTask wfTask = wfTaskService.getById(wfTaskDto.getId());
                Assert.notNull(wfTask, "未查询到任务");
                Assert.isTrue(wfTask.getTaskState() == WfTaskStateEnums.PRE_SUBMIT, "该任务状态不为待提交，无法提交");
                Long instanceId = wfTaskDto.getInstanceId();
                if (instanceId == null) {
                    instanceId = wfTask.getInstanceId();
                    wfTaskDto.setInstanceId(instanceId);
                }
                WfInstance wfInstance = wfInstanceService.getById(instanceId);
                Assert.notNull(wfInstance, "未查询到流程实例");
                if (wfInstance.getInstanceState() != WfInstanceStateEnums.RUNNING) {
                    throw new WfRuntimeException("流程实例状态不为运行中，无法退回");
                }
                // 没有下级节点，或者“所有下级节点”状态都是“已审批”
                long count = wfTaskService.count(Wrappers.<WfTask>lambdaQuery()
                        .eq(WfTask::getInstanceId, instanceId)
                        .eq(WfTask::getParentNodeId, wfTask.getNodeId())
                        .isNull(WfTask::getCentralizeDimDefineId)
                        .ne(WfTask::getTaskState, WfTaskStateEnums.APPROVED)
                );
                if (count > 0) {
                    throw new WfRuntimeException("下级有不是已审批状态的节点，不能向上提交！");
                }

                wfTask.setOperateUserId(String.valueOf(SecurityUtils.getUserId()));
                wfTask.setTaskState(StringUtils.isEmpty(wfTask.getParentNodeId()) ? WfTaskStateEnums.APPROVED : WfTaskStateEnums.PRE_APPROVE);
//                wfTask.setTaskDone("1");
                wfTask.setApproveTime(new Date());
                wfTask.setCommentContent(wfTaskDto.getCommentContent());
                wfTask.setCost(DateUtil.betweenMs(wfTask.getApproveTime(), wfTask.getCreateTime()));
                wfTask.setOperateType(WfOperateTypeEnums.SUBMIT);
                wfTaskService.handle(wfTask);
                // 流程变量
                LambdaUpdateWrapper<WfVariable> wfVariableLambdaUpdateWrapper = Wrappers.lambdaUpdate();
                // 归口节点审批
                if (wfTask.getCentralizeDimDefineId() == null) {
                    wfVariableLambdaUpdateWrapper.isNull(WfVariable::getCentralizeId);
                } else {
                    wfVariableLambdaUpdateWrapper.eq(WfVariable::getCentralizeId, wfTask.getCentralizeDimDefineId());
                }
                wfVariableLambdaUpdateWrapper.eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                        .eq(WfVariable::getNodeId, wfTask.getNodeId())
                        .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
                        .set(WfVariable::getVariableVal, wfTask.getTaskState().name());
                wfVariableService.update(wfVariableLambdaUpdateWrapper);

                // 全部审批通过，结束流程实例
                if (wfTask.getCentralizeDimDefineId() == null && StringUtils.isEmpty(wfTask.getParentNodeId())) {
                    List<WfTask> list = wfTaskService.list(Wrappers.<WfTask>lambdaQuery()
                            .eq(WfTask::getInstanceId, wfInstance.getId())
                            .ne(WfTask::getTaskState, WfTaskStateEnums.APPROVED)
                    );
                    if (CollectionUtils.isEmpty(list)) {
                        // 发布流程结束事件
                        WfEventSource processEndEvent = new WfEventSource();
                        BeanUtils.copyProperties(wfInstance, processEndEvent);
                        processEndEvent.setEventType(WfEventTypeEnums.PROCESS_END);
                        processEndEvent.setInstanceId(wfInstance.getId());
                        processEndEvent.setUserId(String.valueOf(SecurityUtils.getUserId()));
                        processEndEvent.setUserName(SecurityUtils.getUsername());
                        SpringUtils.publishEvent(new WfEvent(processEndEvent));
                    }
                }
                break;
        }
        return AjaxResult.success("提交成功");
    }

}
