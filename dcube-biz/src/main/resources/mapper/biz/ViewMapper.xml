<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.biz.mapper.ViewMapper">

    <select id="queryList" resultType="com.dcube.biz.vo.SimpleViewVo">
        select a.id                     as  id,
               a.view_name              as  viewName,
               a.view_script            as  viewScript,
               a.view_meta              as  viewMeta,
               b.source_type            as  sourceType,
               b.source_config          as  sourceConfig
        from cube_view a left join cube_source b on a.id = b.id
        <where>
            <if test="viewName != null and viewName != ''">
                AND a.view_name like concat('%', #{viewName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND a.status = #{status}
            </if>
        </where>
    </select>
</mapper>