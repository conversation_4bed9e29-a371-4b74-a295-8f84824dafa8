package com.dcube.tran.plugin;

import cn.hutool.core.collection.CollUtil;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.tran.backup.AbstractBackupRepository;
import com.dcube.tran.element.*;
import com.dcube.tran.element.Record;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordReceiver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class BackupRdbmsWriter extends Writer {

    private static final int BATCH_SIZE = 1024 * 5;

    private AbstractBackupRepository repository;

    private List<String> columnCodeList;

    private Triple<List<String>, List<Integer>, List<String>> resultSetMetaData;

    private Connection connection;

    private ReportDto reportDto;

    private String backupVersionName;

    private Date baseDataDt;

    private BackupRdbmsWriter() {

    }

    public BackupRdbmsWriter(AbstractBackupRepository repository, List<String> columnCodeList, ReportDto reportDto, String backupVersionName, Date baseDataDt) {
        super();
        this.repository = repository;
        this.columnCodeList = columnCodeList;
        this.reportDto = reportDto;
        this.backupVersionName = backupVersionName;
        this.baseDataDt = baseDataDt;
    }

    @Override
    public void startWrite(RecordReceiver recordReceiver) {
        List<Record> writeBuffer = new ArrayList<>(BATCH_SIZE);
        try {
            // 兼容mysql列名关键字
            if (JdbcUtils.DB_TYPE_MYSQL.equals(this.repository.getDbType())) {
                this.resultSetMetaData = this.repository.getAbstractRepository().getColumnMetaData("`" + String.join("` , `", columnCodeList) + "`");
            } else {
                this.resultSetMetaData = this.repository.getAbstractRepository().getColumnMetaData(String.join(" , ", columnCodeList));
            }

            Record record;
            while (getTaskHelp().checkStatus()) {
                record = recordReceiver.getFromReader(10);
                if (record != null) {
                    if (record instanceof TerminateRecord) {
                        break;
                    }
                    Column backupVersionNameColumn = new StringColumn();
                    backupVersionNameColumn.setColumnName("backup_version_name");
                    backupVersionNameColumn.setColumn(backupVersionName);
                    record.addColumn(backupVersionNameColumn);

                    //设置base_data_dt列
                    DateColumn baseDataDtColumn = new DateColumn(baseDataDt);
                    baseDataDtColumn.setColumnName("base_data_dt");
                    record.addColumn(baseDataDtColumn);
                    writeBuffer.add(record);
                    if (writeBuffer.size() >= BATCH_SIZE) {
                        doBatchInsert(writeBuffer);
                        writeBuffer.clear();
                    }
                }

            }
            if (getTaskHelp().checkStatus() && !writeBuffer.isEmpty()) {
                doBatchInsert(writeBuffer);
                writeBuffer.clear();
            }
            if (!writeBuffer.isEmpty()) {
                log.error("❕有数据未写入数据库，数据条数：{}，任务状态：{}", writeBuffer.size(), getTaskHelp().checkStatus());
                doBatchInsert(writeBuffer);
                writeBuffer.clear();
            }
            getTaskHelp().setWriterStatus(true);
        } catch (Exception e) {
            log.error("{} PreWriter is error.", CommonErrorCode.RUNTIME_ERROR, e);
            getTaskHelp().setWriterStatus(false);
            getTaskHelp().setMsg(ExceptionUtil.getLocalizedMessage(e));
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "PreWriter is error.", e);
        } finally {
            writeBuffer.clear();
            repository = null;
            JdbcUtils.close(connection, null, null);
            TaskReport.finished(this.reportDto);
        }
    }

    private void doBatchInsert(List<Record> recordList) throws SQLException {
        if (CollUtil.isNotEmpty(recordList)) {
            if (connection == null) {
                connection = JdbcUtils.getConnection(repository.getConfig());
            }
            this.repository.getAbstractRepository().executeBatchSql(connection, columnCodeList, recordList, this.resultSetMetaData);
            // 记录报告进度
            ReportTool.appendRecord(this.reportDto, recordList.size());
        }
    }
}