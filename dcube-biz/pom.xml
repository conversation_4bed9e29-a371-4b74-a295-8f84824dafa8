<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dcube</artifactId>
        <groupId>com.dcube</groupId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>dcube-biz</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.dcube</groupId>
            <artifactId>dcube-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcube</groupId>
            <artifactId>dcube-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcube</groupId>
            <artifactId>dcube-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <!-- Alibaba Fastjson -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.calcite</groupId>
            <artifactId>calcite-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.calcite</groupId>
            <artifactId>calcite-linq4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>qlexpress4</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.idev.excel</groupId>
            <artifactId>fastexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcube</groupId>
            <artifactId>dcube-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>org.roaringbitmap</groupId>
            <artifactId>RoaringBitmap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>dashscope-sdk-java</artifactId>-->
        <!--            &lt;!&ndash; 请将 'the-latest-version' 替换为最新版本号：https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java &ndash;&gt;-->
        <!--            <version>2.18.3</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>slf4j-simple</artifactId>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <!-- Reactor 额外支持 -->
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>

        <!-- WebClient 支持 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty-http</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-core</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-dashscope</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-open-ai</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-ollama</artifactId>
        </dependency>

        <!-- MCP 支持 -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-mcp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.openjdk.jol</groupId>
            <artifactId>jol-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.graalvm.polyglot</groupId>
            <artifactId>polyglot</artifactId>
        </dependency>

        <dependency>
            <groupId>org.graalvm.polyglot</groupId>
            <artifactId>js</artifactId>
            <type>pom</type>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>com.github.wvengen</groupId>-->
            <!--                <artifactId>proguard-maven-plugin</artifactId>-->
            <!--                <version>${proguard.maven.plugin.version}</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>proguard</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <configuration>-->
            <!--                    <proguardVersion>${proguard.base.version}</proguardVersion>-->
            <!--                    <injar>${project.build.finalName}.jar</injar>-->
            <!--                    <outjar>${project.build.finalName}.jar</outjar>-->
            <!--                    &lt;!&ndash; 输出路径配置，但是要注意这个路径必须要包括injar标签填写的jar &ndash;&gt;-->
            <!--                    <outputDirectory>${project.basedir}/target</outputDirectory>-->
            <!--                    <obfuscate>true</obfuscate>-->
            <!--                    &lt;!&ndash; 将pom.xml打包至jar文件中 &ndash;&gt;-->
            <!--                    <addMavenDescriptor>true</addMavenDescriptor>-->
            <!--                    <proguardInclude>${project.basedir}/proguard.cfg</proguardInclude>-->
            <!--                    <injarNotExistsSkip>true</injarNotExistsSkip>-->
            <!--                    &lt;!&ndash; 把jar包放到临时目录以便缩短命令行 &ndash;&gt;-->
            <!--                    <putLibraryJarsInTempDir>true</putLibraryJarsInTempDir>-->
            <!--                    <libs>-->
            <!--                        &lt;!&ndash;Put here your libraries if required&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        java11&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        <lib>${java.home}/jmods/java.base.jmod</lib>&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        <lib>${java.home}/jmods/java.datatransfer.jmod</lib>&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        <lib>${java.home}/jmods/java.prefs.jmod</lib>&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        <lib>${java.home}/jmods/java.xml.jmod</lib>&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        <lib>${java.home}/jmods/java.desktop.jmod</lib>&ndash;&gt;-->
            <!--                        &lt;!&ndash;                        <lib>${java.home}/jmods/</lib>&ndash;&gt;-->


            <!--                        &lt;!&ndash; java8&ndash;&gt;-->
            <!--                        &lt;!&ndash;Put here your libraries if required&ndash;&gt;-->
            <!--                        <lib>${java.home}/lib/rt.jar</lib>-->
            <!--                        &lt;!&ndash;<lib>/Library/Java/JavaVirtualMachines/jdk1.8.0_341.jdk/Contents/Home/jre/lib/rt.jar</lib>&ndash;&gt;-->
            <!--                    </libs>-->
            <!--                </configuration>-->
            <!--                <dependencies>-->
            <!--                    <dependency>-->
            <!--                        <groupId>com.guardsquare</groupId>-->
            <!--                        <artifactId>proguard-base</artifactId>-->
            <!--                        <version>${proguard.base.version}</version>-->
            <!--                        <scope>runtime</scope>-->
            <!--                    </dependency>-->
            <!--                    <dependency>-->
            <!--                        <groupId>com.guardsquare</groupId>-->
            <!--                        <artifactId>proguard-core</artifactId>-->
            <!--                        <version>${proguard.core.version}</version>-->
            <!--                        <scope>runtime</scope>-->
            <!--                    </dependency>-->
            <!--                </dependencies>-->
            <!--            </plugin>-->
        </plugins>
    </build>
</project>