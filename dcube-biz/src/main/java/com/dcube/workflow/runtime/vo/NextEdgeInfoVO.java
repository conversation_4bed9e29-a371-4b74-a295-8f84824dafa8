package com.dcube.workflow.runtime.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode
@ToString
public class NextEdgeInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String currentNode;

    private List<SubmitInfoEdgeVO> submitEdge;

    private String nextNodeName;

    public List<SubmitInfoEdgeVO> getSubmitEdge() {
        if (submitEdge == null) {
            submitEdge = new ArrayList<>();
        }
        return submitEdge;
    }

}


