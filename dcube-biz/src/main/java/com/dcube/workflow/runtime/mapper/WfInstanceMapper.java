package com.dcube.workflow.runtime.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcube.workflow.runtime.domain.WfInstance;

import java.util.List;

/**
 * 流程运行实例Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface WfInstanceMapper extends BaseMapper<WfInstance> {
    /**
     * 查询流程运行实例
     *
     * @param id 流程运行实例主键
     * @return 流程运行实例
     */
    WfInstance selectCubeWfInstanceById(Long id);

    /**
     * 查询流程运行实例列表
     *
     * @param wfInstance 流程运行实例
     * @return 流程运行实例集合
     */
    List<WfInstance> selectCubeWfInstanceList(WfInstance wfInstance);

    /**
     * 新增流程运行实例
     *
     * @param wfInstance 流程运行实例
     * @return 结果
     */
    int insertCubeWfInstance(WfInstance wfInstance);

    /**
     * 修改流程运行实例
     *
     * @param wfInstance 流程运行实例
     * @return 结果
     */
    int updateCubeWfInstance(WfInstance wfInstance);

    /**
     * 删除流程运行实例
     *
     * @param id 流程运行实例主键
     * @return 结果
     */
    int deleteCubeWfInstanceById(Long id);

    /**
     * 批量删除流程运行实例
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCubeWfInstanceByIds(Long[] ids);
}
