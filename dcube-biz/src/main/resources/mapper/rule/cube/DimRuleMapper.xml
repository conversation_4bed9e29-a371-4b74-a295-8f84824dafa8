<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.rule.cube.mapper.DimRuleMapper">
    <sql id="Base_Column_List">
        id, dim_table_id, rule_name, rule_type, order_num, position, version_num,
        create_by, create_time, update_by, update_time
    </sql>

    <select id="getPreviousDimRuleByPosition" resultType="com.dcube.rule.cube.domain.DimRule">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_rule
        WHERE dim_table_id = #{dimTableId}
        AND position = (
        SELECT MAX(s.position)
        FROM cube_dim_rule s
        WHERE
        s.del_flag = '0'
        AND s.dim_table_id = #{dimTableId}
        AND s.position &lt; #{position}
        ) and del_flag = '0'
        LIMIT 1
    </select>

    <select id="getNextDimRuleByPosition" resultType="com.dcube.rule.cube.domain.DimRule">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_rule
        WHERE dim_table_id = #{dimTableId}
        AND position = (
        SELECT MIN(s.position)
        FROM cube_dim_rule s
        WHERE s.del_flag = '0'
        AND s.dim_table_id = #{dimTableId}
        AND s.position &gt; #{position}
        ) and del_flag = '0'
        LIMIT 1
    </select>

    <update id="updateByVersion">
        UPDATE cube_dim_rule
        SET version_num = version_num + 1
        WHERE ID = #{id}
          AND version_num = #{versionNum}
          and del_flag = '0'
    </update>

    <select id="getMaxPosition" resultType="com.dcube.rule.cube.domain.DimRule">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cube_dim_rule
        WHERE dim_table_id = #{dimTableId} and position = (
        SELECT
        MAX(s.position)
        FROM cube_dim_rule s
        WHERE
        s.dim_table_id = #{dimTableId} and s.del_flag = '0'
        ) and del_flag = '0'
        LIMIT 1
    </select>

</mapper>