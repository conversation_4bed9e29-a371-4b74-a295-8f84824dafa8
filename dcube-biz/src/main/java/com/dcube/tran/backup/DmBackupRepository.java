package com.dcube.tran.backup;

import cn.hutool.core.date.DateUtil;
import com.dcube.biz.constant.enums.TableBackupColumnTypeEnum;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class DmBackupRepository extends AbstractBackupRepository {

    private static final String COLUMN_SQL_FORMAT = "%s %s %s";
    private static final String ADD_COLUMN_SQL_FORMAT = "alter table %s add column " + COLUMN_SQL_FORMAT;
    private static final String COMMENT_TABLE_SQL_FORMAT = "COMMENT ON TABLE %s IS '%s'";
    private static final String COMMENT_COLUMN_SQL_FORMAT = "COMMENT ON COLUMN %s.%s IS '%s';";

    DmBackupRepository(String dbType, SourceConfigJson config, String tableName, Map<String, TableBackupConfig> backupConfigMap, int tableLevel, String tableComment) {
        super(dbType, config, tableName, backupConfigMap, tableLevel, tableComment);
    }

    @Override
    public String getIntColumn() {
        return "integer";
    }

    @Override
    public String getBigintColumn() {
        return "bigint";
    }

    @Override
    public String getStringColumn() {
        return "varchar2(%s)";
    }

    @Override
    public String getNumericColumn() {
        return "numeric(%s,%s)";
    }

    @Override
    public String getDatetimeColumn() {
        return "datetime";
    }

    @Override
    public void createTable() {
        Map<String, TableBackupConfig> backupConfigMap = getBackupConfigMap();
        List<String> columns = new ArrayList<>(backupConfigMap.size());
        List<String> comments = new ArrayList<>(backupConfigMap.size());
        int tableLevel = getTableLevel();
        List<String> primaryKeyList = new ArrayList<>(tableLevel);
        for (Map.Entry<String, TableBackupConfig> entry : backupConfigMap.entrySet()) {
            TableBackupConfig tableBackupConfig = entry.getValue();
            String columnName = tableBackupConfig.getBackupColumnName().toUpperCase();
            TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
            switch (columnType) {
                case INTEGER:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getIntColumn()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL));
                    break;
                case BIGINT:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getBigintColumn()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL));
                    break;
                case VARCHAR:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getStringColumn(), tableBackupConfig.getColumnLength()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL));
                    break;
                case NUMERIC:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL));
                    break;
                case DATETIME:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getDatetimeColumn()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL));
                    break;
                default:
                    log.error("Unsupported type: " + columnType);
                    break;
            }
            if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
                tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
            }
            // 添加注释
            comments.add(String.format(COMMENT_COLUMN_SQL_FORMAT, getTableName(), columnName, tableBackupConfig.getRemark()));
            if (tableBackupConfig.isPrimaryKey()) {
                primaryKeyList.add(entry.getKey());
            }
        }
        // 添加备份版本名称字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "BACKUP_VERSION_NAME", String.format(getStringColumn(), 128), NOT_NULL));
        // 添加注释
        comments.add(String.format(COMMENT_COLUMN_SQL_FORMAT, getTableName(), "BACKUP_VERSION_NAME", "备份版本名称"));
        // 表注释
        comments.add(genTableCommentSql());
        // 添加数据日期字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "BASE_DATA_DT", String.format(getDatetimeColumn()), NOT_NULL));
        // 添加注释
        comments.add(String.format(COMMENT_COLUMN_SQL_FORMAT, getTableName(), "BASE_DATA_DT", "数据日期"));
        // 表注释
        comments.add(genTableCommentSql());
        // 指定联合主键
        columns.add(String.format("PRIMARY KEY (BACKUP_VERSION_NAME,BASE_DATA_DT,%s) ", String.join(",", primaryKeyList)));
        String sql = String.format("create table %s (%s)", getTableName(), String.join(",", columns));
        super.getAbstractRepository().executeSql(sql);
        // 不支持放在一块执行
        comments.forEach(comment -> super.getAbstractRepository().executeSql(comment));
    }


    @Override
    public String genAddColumnSql(TableBackupConfig tableBackupConfig) {
        String sql = "";
        TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
        String columnName = tableBackupConfig.getBackupColumnName().toUpperCase();
        switch (columnType) {
            case INTEGER:
                sql = String.format(ADD_COLUMN_SQL_FORMAT, getTableName(), columnName, String.format(getIntColumn()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL);
                break;
            case BIGINT:
                sql = String.format(ADD_COLUMN_SQL_FORMAT, getTableName(), columnName, String.format(getBigintColumn()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL);
                break;
            case VARCHAR:
                sql = String.format(ADD_COLUMN_SQL_FORMAT, getTableName(), columnName, String.format(getStringColumn(), tableBackupConfig.getColumnLength()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL);
                break;
            case NUMERIC:
                sql = String.format(ADD_COLUMN_SQL_FORMAT, getTableName(), columnName, String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL);
                break;
            case DATETIME:
                sql = String.format(ADD_COLUMN_SQL_FORMAT, getTableName(), columnName, String.format(getDatetimeColumn()), tableBackupConfig.isPrimaryKey() ? NOT_NULL : NULL);
                break;
            default:
                log.error("Unsupported type: " + columnType);
                break;
        }
        sql += ";\n";
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }
        // 添加注释
        sql += String.format(COMMENT_COLUMN_SQL_FORMAT, getTableName(), columnName, tableBackupConfig.getRemark());
        return sql;
    }

    @Override
    public String genRenameColumnSql(String oldColumnName, String newColumnName) {
        return String.format("ALTER TABLE %s RENAME COLUMN %s TO %s", getTableName(), oldColumnName, newColumnName);
    }

    @Override
    public boolean checkColumnTypeName(List<String> sqlList, String columnType, TableBackupConfig tableBackupConfig, int columnSize, String remarks) {
        boolean flag = false;
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }
        switch (tableBackupConfig.getColumnType()) {
            case INTEGER:
                flag = columnType.equalsIgnoreCase("INT") || columnType.equalsIgnoreCase("INTEGER");
                if (!flag) {
                    if (sqlList != null) {
                        sqlList.add(MessageFormat.format("alter table {0} modify {1} {2}\n", getTableName(), tableBackupConfig.getBackupColumnName(), tableBackupConfig.getColumnType()));
                    }
                }
                break;
            case BIGINT:
                flag = columnType.equalsIgnoreCase("BIGINT");
                if (!flag) {
                    if (sqlList != null) {
                        sqlList.add(MessageFormat.format("alter table {0} modify {1} {2}\n", getTableName(), tableBackupConfig.getBackupColumnName(), tableBackupConfig.getColumnType()));
                    }
                }
                break;
            case VARCHAR:
                flag = columnType.equalsIgnoreCase("VARCHAR") || columnType.equalsIgnoreCase("VARCHAR2");
                if (flag) {
                    // 判断字段长度是否一致，不一致需要使用alter
                    flag = checkColumnLength(tableBackupConfig, columnSize);
                }
                if (!flag) {
                    if (sqlList != null) {
                        sqlList.add(MessageFormat.format("alter table {0} modify {1} {2}({3})\n", getTableName(), tableBackupConfig.getBackupColumnName(), tableBackupConfig.getColumnType(), tableBackupConfig.getColumnLength()));
                    }
                }
                break;
            case NUMERIC:
                flag = columnType.equalsIgnoreCase("NUMERIC") || columnType.equalsIgnoreCase("DECIMAL");
                if (flag) {
                    flag = checkColumnLength(tableBackupConfig, columnSize);
                }
                if (!flag) {
                    if (sqlList != null) {
                        sqlList.add(MessageFormat.format("alter table {0} modify {1} {2}({3},8)\n", getTableName(), tableBackupConfig.getBackupColumnName(), tableBackupConfig.getColumnType(), tableBackupConfig.getColumnLength()));
                    }
                }
                break;
            case DATETIME:
                flag = columnType.equalsIgnoreCase("DATETIME") || columnType.equalsIgnoreCase("TIMESTAMP");
                if (!flag) {
                    if (sqlList != null) {
                        sqlList.add(MessageFormat.format("alter table {0} modify {1} {2}\n", getTableName(), tableBackupConfig.getBackupColumnName(), tableBackupConfig.getColumnType()));
                    }
                }
                break;
        }
        if (flag) {
            if (!StringUtils.equals(remarks, tableBackupConfig.getRemark())) {
                flag = false;
                if (sqlList != null) {
                    sqlList.add(String.format(COMMENT_COLUMN_SQL_FORMAT, getTableName(), tableBackupConfig.getBackupColumnName(), tableBackupConfig.getRemark()));
                }
            }
        }
        return flag;
    }

    @Override
    public String genTableCommentSql() {
        return String.format(COMMENT_TABLE_SQL_FORMAT, getTableName(), getTableComment());
    }

    private boolean checkColumnLength(TableBackupConfig tableBackupConfig, int columnSize) {
        // 判断字段长度是否一致，不一致需要使用alter
        return tableBackupConfig.getColumnLength() == null || columnSize == tableBackupConfig.getColumnLength();
    }

    @Override
    public void deleteData(String backupTableName, String versionName, Date baseDataDt) {
        super.getAbstractRepository().executeSql(String.format("delete from %s where backup_version_name = '%s' and base_data_dt = '%s'", backupTableName, versionName, DateUtil.formatDate(baseDataDt)));
    }

}
