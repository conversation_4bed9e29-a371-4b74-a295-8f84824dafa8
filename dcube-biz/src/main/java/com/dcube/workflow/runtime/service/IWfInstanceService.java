package com.dcube.workflow.runtime.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.dto.WfInstanceStartDTO;

import java.util.List;

/**
 * 流程运行实例Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IWfInstanceService extends IService<WfInstance> {
    /**
     * 查询流程运行实例
     *
     * @param id 流程运行实例主键
     * @return 流程运行实例
     */
    WfInstance selectCubeWfInstanceById(Long id);

    /**
     * 查询流程运行实例列表
     *
     * @param wfInstance 流程运行实例
     * @return 流程运行实例集合
     */
    List<WfInstance> selectCubeWfInstanceList(WfInstance wfInstance);

    /**
     * 新增流程运行实例
     *
     * @param wfInstance 流程运行实例
     * @return 结果
     */
    int insertCubeWfInstance(WfInstance wfInstance);

    /**
     * 修改流程运行实例
     *
     * @param wfInstance 流程运行实例
     * @return 结果
     */
    int updateCubeWfInstance(WfInstance wfInstance);

    /**
     * 批量删除流程运行实例
     *
     * @param ids 需要删除的流程运行实例主键集合
     * @return 结果
     */
    int deleteCubeWfInstanceByIds(Long[] ids);

    /**
     * 删除流程运行实例信息
     *
     * @param id 流程运行实例主键
     * @return 结果
     */
    int deleteCubeWfInstanceById(Long id);

    /**
     * 开启流程
     *
     * @param wfInstanceStartDto 开始流程
     * @return 流程运行实例
     */
    WfInstance startProcess(WfInstanceStartDTO wfInstanceStartDto);

    List<FlowChart.EdgeDTO> getNextEdges(Integer tableId, Long instanceId, String nodeId, String dataId);

    FlowChart.NodeDTO getCurrentNode(Integer tableId, Long instanceId);

    WfInstance stopProcess(WfInstanceStartDTO wfInstanceStartDto);
}
