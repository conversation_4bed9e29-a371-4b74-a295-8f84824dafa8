package com.dcube.workflow.runtime.controller;

import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import com.dcube.workflow.runtime.domain.WfHistory;
import com.dcube.workflow.runtime.service.IWfHistoryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 流程历史任务Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@RestController
@RequestMapping("/workflow/runtime/history")
@Tag(name = "DCUBE-流程任务历史", description = "DCUBE-流程任务历史")
public class WfHistoryController extends BaseController {
    @Autowired
    private IWfHistoryService cubeWfHistoryService;

    /**
     * 查询流程历史任务列表
     */
    @Operation(summary = "查询流程历史任务列表")
    @GetMapping("/list")
    public AjaxResult list(WfHistory wfHistory) {
        return AjaxResult.success(cubeWfHistoryService.selectCubeWfHistoryList(wfHistory));
    }

    /**
     * 获取流程历史任务详细信息
     */
    @Operation(summary = "获取流程历史任务详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(cubeWfHistoryService.selectCubeWfHistoryById(id));
    }

    /**
     * 新增流程历史任务
     */
    @Operation(summary = "新增流程历史任务")
    @Log(title = "流程历史任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WfHistory wfHistory) {
        return toAjax(cubeWfHistoryService.insertCubeWfHistory(wfHistory));
    }

    /**
     * 修改流程历史任务
     */
    @Operation(summary = "修改流程历史任务")
    @Log(title = "流程历史任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WfHistory wfHistory) {
        return toAjax(cubeWfHistoryService.updateCubeWfHistory(wfHistory));
    }

    /**
     * 删除流程历史任务
     */
    @Operation(summary = "删除流程历史任务")
    @Log(title = "流程历史任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(cubeWfHistoryService.deleteCubeWfHistoryByIds(ids));
    }

    /**
     * 根据流程实例id查询历史任务
     */
    @Operation(summary = "根据流程实例id查询历史任务")
    @GetMapping("/getByInstanceId")
    public AjaxResult getByInstanceId(@RequestParam("instanceId") Long instanceId) {
        return AjaxResult.success(cubeWfHistoryService.getByInstanceId(instanceId));
    }

}
