package com.dcube.tran.plugin;

import com.dcube.common.dto.ReportDto;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.element.Column;
import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordReceiver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class GridWriter extends Writer {

    protected static final int BATCH_SIZE = 1024 * 5;

    protected TableMetaData tableMetaData;

    protected ReportDto reportDto;

    protected String tableName;

    public GridWriter() {

    }

    public GridWriter(String tableName, TableMetaData tableMetaData, ReportDto reportDto) {
        super();
        this.tableName = tableName;
        this.tableMetaData = tableMetaData;
        this.reportDto = reportDto;
    }

    @Override
    public void startWrite(RecordReceiver recordReceiver) {
        List<Object[]> writeBuffer = null;
        List<String> columnNames = tableMetaData.getColumnNames().stream().map(StringUtils::upperCase).collect(Collectors.toList());
        List<String> columnNameComments = tableMetaData.getColumnNameComments();
        List<Class<?>> columnTypes = tableMetaData.getColumnTypes();
        int rowSize = columnTypes.size();
        int curIndex = 0;
        try {
            MemorySchema.getDatabase().createTable(tableMetaData);
            Record record;
            writeBuffer = new ArrayList<>(this.reportDto.getTotalRecord().intValue());
            while (getTaskHelp().checkStatus()) {
                record = recordReceiver.getFromReader(10);
                if (record == null) {
                    continue;
                }
                if (record instanceof TerminateRecord) {
                    break;
                }

                Map<String, Column> columnMap = StreamUtils.toMap(record.getColumnList(), column -> StringUtils.upperCase(column.getColumnName()), Function.identity());
                Object[] row = new Object[rowSize];
                for (int i = 0; i < rowSize; i++) {
                    String columnName = columnNames.get(i);
                    Column col = MapUtils.getObject(columnMap, columnName);
                    row[i] = (col != null) ? getValue(columnNameComments.get(i), columnTypes.get(i), col) : null;
                }

                writeBuffer.add(row);
                curIndex++;
                if (curIndex >= BATCH_SIZE) {
                    ReportTool.appendRecord(this.reportDto, curIndex);
                    curIndex = 0;
                }
            }
            if (getTaskHelp().checkStatus() && curIndex > 0) {
                ReportTool.appendRecord(this.reportDto, curIndex);
            }

            getTaskHelp().setWriterStatus(true);
            MemorySchema.getDatabase().reData(tableName, writeBuffer);
        } catch (Exception e) {
            log.error("{} PreWriter is error.", CommonErrorCode.RUNTIME_ERROR, e);
            getTaskHelp().setWriterStatus(false);
            getTaskHelp().setMsg(e.getLocalizedMessage());
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "PreWriter is error.", e);
        } finally {
            if (writeBuffer != null) {
                writeBuffer.clear();
                writeBuffer = null;
            }
            TaskReport.finished(this.reportDto);
        }
    }

    protected Object getValue(String columnName, Class clazz, Column columnValue) {
        if (columnValue.getRawData() == null) {
            return null;
        }
        try {
            if (Integer.class.equals(clazz)) {
                return columnValue.asLong().intValue();
            } else if (Double.class.equals(clazz)) {
                return columnValue.asDouble();
            } else if (String.class.equals(clazz)) {
                return columnValue.asString();
            } else if (Date.class.equals(clazz)) {
                return new java.sql.Timestamp(columnValue.asDate().getTime());
            }
        } catch (Exception e) {
            throw new ServiceException("列【" + columnName + "】转换失败，原因为: " + e.getLocalizedMessage());
        }
        throw new IllegalArgumentException("列【" + columnName + "】存在不支持的类型: " + clazz.getName());
    }
}