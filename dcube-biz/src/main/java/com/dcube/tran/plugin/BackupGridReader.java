package com.dcube.tran.plugin;

import com.dcube.common.dto.ReportDto;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.dcube.tran.element.*;
import com.dcube.tran.element.Record;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordSender;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.sql.Date;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class BackupGridReader extends GridReader {

    private Map<String, String> columnNameMapping;

    private Map<String, Integer> columnIndexMap;
    private int columnCount;
    private String[] columnNamesArray;
    private Class<?>[] columnTypesArray;

    public BackupGridReader() {

    }

    public BackupGridReader(String tableName, ReportDto reportDto, List<String> columnList, Map<String, String> columnNameMapping) {
        super(tableName, reportDto);
        this.columnNameMapping = columnNameMapping;
        int size = columnList.size();
        this.columnIndexMap = Maps.newHashMapWithExpectedSize(size);
        for (int i = 0; i < size; i++) {
            String column = columnList.get(i);
            this.columnIndexMap.put(StringUtils.lowerCase(column), i);
        }
        List<String> columnNames = tableMetaData.getColumnNames();
        columnCount = columnNames.size();
        columnNamesArray = tableMetaData.getColumnNames().toArray(new String[0]);

        List<Class<?>> columnTypes = tableMetaData.getColumnTypes();
        columnTypesArray = columnTypes.toArray(new Class<?>[0]);

    }

    @Override
    public void startRead(RecordSender recordSender) {
        log.info("============================================={}==============================================", "执行GridReader");
        try {
            Object[][] tableData = MemorySchema.getDatabase().getTableDataMap().get(tableName);
            int length = tableData.length;
            reportDto.setTotalRecord((long) length);
            int i = 0;
            while (getTaskHelp().checkStatus() && i < length) {
                Object[] row = tableData[i];
                recordSender.sendToWriter(buildRecord(row), waitSeconds);
                i++;
            }
            getTaskHelp().setReaderStatus(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            getTaskHelp().setReaderStatus(false);
            getTaskHelp().setMsg(e.getMessage());
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, e);
        }
    }

    private Record buildRecord(Object[] row) {
        Column[] columns = new Column[columnCount];
        int i = 0;
        try {
            for (; i < columnCount; i++) {
                String columnName = columnNamesArray[i];
                columnName = MapUtils.getString(columnNameMapping, columnName, columnName);
                Integer index = MapUtils.getObject(columnIndexMap, StringUtils.lowerCase(columnName));
                Object value = row[i];
                if (String.class.equals(columnTypesArray[i])) {
                    StringColumn stringColumn1 = new StringColumn(value == null ? null : (String) value);
                    stringColumn1.setColumnName(columnName);
                    columns[index] = stringColumn1;
                } else if (Double.class.equals(columnTypesArray[i])) {
                    DoubleColumn doubleColumn = new DoubleColumn(value == null ? null : (double) value);
                    doubleColumn.setColumnName(columnName);
                    columns[index] = doubleColumn;
                } else if (Integer.class.equals(columnTypesArray[i])) {
                    LongColumn longColumn1 = new LongColumn(value == null ? null : Long.valueOf(value.toString()));
                    longColumn1.setColumnName(columnName);
                    columns[index] = longColumn1;
                } else if (Date.class.equals(columnTypesArray[i])) {
                    DateColumn dateColumn = new DateColumn(value == null ? null : (Date) value);
                    dateColumn.setColumnName(columnName);
                    columns[index] = dateColumn;
                } else if (java.util.Date.class.equals(columnTypesArray[i])) {
                    DateColumn dateColumn = new DateColumn(value == null ? null : (java.util.Date) value);
                    dateColumn.setColumnName(columnName);
                    columns[index] = dateColumn;
                } else {
                    throw DataTranException
                            .asDataTranException(
                                    CommonErrorCode.READ_NOT_SUPPORT,
                                    String.format(
                                            "不支持的字段类型. 字段名:[%s],  字段Java类型:[%s].",
                                            columnName,
                                            columnTypesArray[i]));
                }
            }
        } catch (Exception e) {
            log.error("read index:{} data: {} occur exception:", i, row[i], e);
            throw new ServiceException(e);
        }

        Record record = new DefaultRecord(columnCount);
        record.addColumns(Arrays.asList(columns));
        return record;
    }

}