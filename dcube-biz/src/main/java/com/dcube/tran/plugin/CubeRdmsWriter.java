package com.dcube.tran.plugin;

import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.spi.CubeServer;
import com.dcube.tran.element.CubeRecord;
import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordReceiver;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class CubeRdmsWriter extends Writer {

    private static final int BATCH_SIZE = 1024 * 5;

    private ReportDto reportDto;

    private DimTableInfoVo dimTableInfoVo;

    private Map<String, List<String>> filterDims;

    private CubeServer cubeServer;

    private CubeRdmsWriter() {

    }

    public CubeRdmsWriter(DimTableInfoVo dimTableInfoVo, CubeServer cubeServer, ReportDto reportDto, Map<String, List<String>> filterDims) {
        super();
        this.dimTableInfoVo = dimTableInfoVo;
        this.cubeServer = cubeServer;
        this.reportDto = reportDto;
        this.filterDims = filterDims;
    }

    @Override
    public void startWrite(RecordReceiver recordReceiver) {
        List<FactTable.Record> writeBuffer = new ArrayList<>(BATCH_SIZE);
        try {
            Record record;
            while (getTaskHelp().checkStatus()) {
                record = recordReceiver.getFromReader(10);
                if (record != null) {
                    if (record instanceof TerminateRecord) {
                        break;
                    }
                    CubeRecord cubeRecord = (CubeRecord) record;
                    log.info("CubeRecord：{}", cubeRecord);
                    FactTable.Record factTableRecord = cubeRecord.getRecord();

                    writeBuffer.add(factTableRecord);
                    if (writeBuffer.size() >= BATCH_SIZE) {
                        // 记录报告进度
                        ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                        writeBuffer.clear();
                    }
                }
            }
            if (getTaskHelp().checkStatus() && !writeBuffer.isEmpty()) {
                // 记录报告进度
                ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                writeBuffer.clear();
            }
            getTaskHelp().setWriterStatus(true);
        } catch (Exception e) {
            getTaskHelp().setWriterStatus(false);
            getTaskHelp().setMsg(e.getLocalizedMessage());
            log.error("{} PreWriter is error.", CommonErrorCode.RUNTIME_ERROR, e);
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "PreWriter is error.", e);
        } finally {
            writeBuffer.clear();
            TaskReport.finished(this.reportDto);
        }
    }

}