package com.dcube.workflow.runtime.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.workflow.runtime.domain.WfHistory;
import com.dcube.workflow.runtime.mapper.WfHistoryMapper;
import com.dcube.workflow.runtime.service.IWfHistoryService;
import com.dcube.workflow.runtime.vo.WfHistoryVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 流程历史任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
public class WfHistoryServiceImpl extends ServiceImpl<WfHistoryMapper, WfHistory> implements IWfHistoryService {
    @Autowired
    private WfHistoryMapper wfHistoryMapper;

    /**
     * 查询流程历史任务
     *
     * @param id 流程历史任务主键
     * @return 流程历史任务
     */
    @Override
    public WfHistory selectCubeWfHistoryById(Long id) {
        return wfHistoryMapper.selectCubeWfHistoryById(id);
    }

    /**
     * 查询流程历史任务列表
     *
     * @param wfHistory 流程历史任务
     * @return 流程历史任务
     */
    @Override
    public List<WfHistory> selectCubeWfHistoryList(WfHistory wfHistory) {
        List<WfHistory> wfHistories = wfHistoryMapper.selectCubeWfHistoryList(wfHistory);
        if (CollectionUtils.isNotEmpty(wfHistories)) {
            wfHistories.forEach(history -> {
                if (history.getOperateType() != null) {
                    history.setOperateTypeName(history.getOperateType().getCName());
                }
            });
        }
        return wfHistories;

    }

    /**
     * 新增流程历史任务
     *
     * @param wfHistory 流程历史任务
     * @return 结果
     */
    @Override
    public int insertCubeWfHistory(WfHistory wfHistory) {
        return wfHistoryMapper.insertCubeWfHistory(wfHistory);
    }

    /**
     * 修改流程历史任务
     *
     * @param wfHistory 流程历史任务
     * @return 结果
     */
    @Override
    public int updateCubeWfHistory(WfHistory wfHistory) {
        return wfHistoryMapper.updateCubeWfHistory(wfHistory);
    }

    /**
     * 批量删除流程历史任务
     *
     * @param ids 需要删除的流程历史任务主键
     * @return 结果
     */
    @Override
    public int deleteCubeWfHistoryByIds(Long[] ids) {
        return wfHistoryMapper.deleteCubeWfHistoryByIds(ids);
    }

    /**
     * 删除流程历史任务信息
     *
     * @param id 流程历史任务主键
     * @return 结果
     */
    @Override
    public int deleteCubeWfHistoryById(Long id) {
        return wfHistoryMapper.deleteCubeWfHistoryById(id);
    }

    @Override
    public List<WfHistoryVO> getByInstanceId(Long instanceId) {
        List<WfHistoryVO> historyVOList = this.baseMapper.getByInstanceId(instanceId);
        if (CollectionUtils.isEmpty(historyVOList)) {
            return Collections.emptyList();
        }
        for (WfHistoryVO wfHistoryVO : historyVOList) {
            if (wfHistoryVO.getCost() != null) {
                wfHistoryVO.setStayTime(TimeUnit.MILLISECONDS.toHours(wfHistoryVO.getCost()) + "时");
            }
        }
        return historyVOList;
    }

}
