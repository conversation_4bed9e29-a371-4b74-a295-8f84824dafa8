package com.dcube.tran.element;

import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import org.apache.commons.lang3.ArrayUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class BytesColumn extends Column {

    public BytesColumn() {
        this(null);
    }

    public BytesColumn(byte[] bytes) {
        super(ArrayUtils.clone(bytes), Column.Type.BYTES, null == bytes ? 0
                : bytes.length);
    }

    @Override
    public byte[] asBytes() {
        if (null == this.getRawData()) {
            return null;
        }

        return (byte[]) this.getRawData();
    }

    @Override
    public String asString() {
        if (null == this.getRawData()) {
            return null;
        }

        try {
            return ColumnCast.bytes2String(this);
        } catch (Exception e) {
            throw DataTranException.asDataTranException(
                    CommonErrorCode.CONVERT_NOT_SUPPORT,
                    String.format("Bytes[%s]不能转为String .", this.toString()));
        }
    }

    @Override
    public Long asLong() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Bytes类型不能转为Long .");
    }

    @Override
    public BigDecimal asBigDecimal() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Bytes类型不能转为BigDecimal .");
    }

    @Override
    public BigInteger asBigInteger() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Bytes类型不能转为BigInteger .");
    }

    @Override
    public Double asDouble() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Bytes类型不能转为Long .");
    }

    @Override
    public Date asDate() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Bytes类型不能转为Date .");
    }

    @Override
    public Boolean asBoolean() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Bytes类型不能转为Boolean .");
    }
}
