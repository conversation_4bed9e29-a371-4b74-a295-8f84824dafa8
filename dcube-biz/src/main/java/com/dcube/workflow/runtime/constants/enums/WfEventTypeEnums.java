package com.dcube.workflow.runtime.constants.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WfEventTypeEnums {
    /**
     * 发起流程
     */
    START_PROCESS("0"),
    /**
     * 任务完成
     */
    TASK_HANDLE("1"),
    /**
     * 任务创建
     */
    TASK_CREATED("2"),
    /**
     * 流程结束
     */
    PROCESS_END("3"),
    /**
     * 进入环节
     */
    ENTER_NODE("4"),
    /**
     * 任务撤回
     */
    TASK_WITHDRAW("5"),
    /**
     * 任务退回
     */
    TASK_RETURN("6"),
    ;


    @EnumValue
    private final String state;
}
