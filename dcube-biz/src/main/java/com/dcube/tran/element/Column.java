package com.dcube.tran.element;

import com.alibaba.fastjson2.JSON;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public abstract class Column {

    private String columnName;

    private Type type;

    private Object rawData;

    private int byteSize;

    public Column(final Object object, final Type type, int byteSize) {
        this.rawData = object;
        this.type = type;
        this.byteSize = byteSize;
    }

    public String getColumnName() {
        return this.columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public void setColumn(Object value) {
        this.rawData = value;
        this.type = Type.STRING;
        this.byteSize = (null == rawData ? 0 : String.valueOf(value).length());
    }

    public Object getRawData() {
        return this.rawData;
    }

    public Type getType() {
        return this.type;
    }

    public int getByteSize() {
        return this.byteSize;
    }

    protected void setType(Type type) {
        this.type = type;
    }

    protected void setRawData(Object rawData) {
        this.rawData = rawData;
    }

    protected void setByteSize(int byteSize) {
        this.byteSize = byteSize;
    }

    public abstract Long asLong();

    public abstract Double asDouble();

    public abstract String asString();

    public abstract Date asDate();

    public abstract byte[] asBytes();

    public abstract Boolean asBoolean();

    public abstract BigDecimal asBigDecimal();

    public abstract BigInteger asBigInteger();

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public enum Type {
        BAD, NULL, INT, LONG, DOUBLE, STRING, BOOL, DATE, BYTES
    }
}
