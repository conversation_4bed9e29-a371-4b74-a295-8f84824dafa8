package com.dcube.tran.execution;

import com.dcube.tran.element.Record;
import lombok.Getter;

import java.util.List;
import java.util.Map;

public abstract class AbstractTranExecution {

    public AbstractTranExecution(List<TranParas> tranParasList) {
        this.tranParasList = tranParasList;
    }

    public AbstractTranExecution(Map<String, Object> tranParasMap) {
        this.tranParasMap = tranParasMap;
    }

    @Getter
    private List<TranParas> tranParasList;

    @Getter
    private Map<String, Object> tranParasMap;

    public abstract List<Record> doTran(Record record);
}
