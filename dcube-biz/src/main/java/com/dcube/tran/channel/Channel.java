package com.dcube.tran.channel;

import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import org.apache.commons.lang3.Validate;

import java.util.Collection;

public abstract class Channel {

    protected int taskGroupId;

    protected int capacity;

    protected int byteCapacity;

    protected long flowControlInterval;

    protected volatile boolean isClosed = false;

    public Channel() {
        this.taskGroupId = 1000;
        // (2^10) * (2^2)
        this.capacity = 1 << 12;
        //channel的queue里默认record为1万条。原来为512条
        this.flowControlInterval = 1000;
        //channel的queue默认大小为8M，原来为64M (2^10) * (2^10) * (2^4) = 2^(10+10+4)
        this.byteCapacity = 1 << 24;
    }

    public void close() {
        this.isClosed = true;
    }

    public void open() {
        this.isClosed = false;
    }

    public boolean isClosed() {
        return isClosed;
    }

    public int getTaskGroupId() {
        return this.taskGroupId;
    }

    public int getCapacity() {
        return capacity;
    }

    public void push(final Record r) {
        Validate.notNull(r, "record不能为空.");
        this.doPush(r);
    }

    public boolean push(final Record r, long seconds) {
        Validate.notNull(r, "record不能为空.");
        return this.doPush(r, seconds);
    }

    public void pushTerminate(final TerminateRecord r) {
        Validate.notNull(r, "record不能为空.");
        this.doPush(r);
    }

    public void pushAll(final Collection<Record> rs) {
        Validate.notNull(rs);
        Validate.noNullElements(rs);
        this.doPushAll(rs);
    }

    public Record pull() {
        Record record = this.doPull();
        return record;
    }

    public Record pull(long seconds) {
        Record record = this.doPull(seconds);
        return record;
    }


    public void pullAll(final Collection<Record> rs) {
        Validate.notNull(rs);
        this.doPullAll(rs);
    }

    protected abstract void doPush(Record r);

    protected abstract boolean doPush(Record r, long seconds);

    protected abstract void doPushAll(Collection<Record> rs);

    protected abstract Record doPull();

    protected abstract Record doPull(long seconds);

    protected abstract void doPullAll(Collection<Record> rs);

    public abstract int size();

    public abstract boolean isEmpty();

    public abstract void clear();

    private long getByteSize(final Collection<Record> rs) {
        long size = 0;
        for (final Record each : rs) {
            size += each.getByteSize();
        }
        return size;
    }
}
