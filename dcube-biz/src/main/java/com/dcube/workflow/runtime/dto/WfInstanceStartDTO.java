package com.dcube.workflow.runtime.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class WfInstanceStartDTO extends BaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "二维表id")
    private Integer tableId;

    @Schema(description = "数据id")
    private String dataId;

    @Schema(description = "下一个线id")
    private String nextEdgeId;

    /**
     * 审批意见内容
     */
    @Schema(description = "审批意见内容")
    private String commentContent;

    /**
     * 流程实例类型
     */
    @Schema(description = "流程实例类型")
    private WfInstanceTypeEnums flowType = WfInstanceTypeEnums.TABLE;

    @Schema(description = "多维流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wfDimDefineId;


}
