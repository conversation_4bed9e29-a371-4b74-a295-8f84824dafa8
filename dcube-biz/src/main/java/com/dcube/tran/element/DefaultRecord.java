package com.dcube.tran.element;

import com.alibaba.fastjson2.JSON;
import com.dcube.tran.util.ClassSize;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DefaultRecord implements Record {

    public static final DefaultRecord EMPTY_RECORD = new DefaultRecord();

    public static final List<Record> EMPTY_RECORD_LIST = new ArrayList<Record>() {{
        add(EMPTY_RECORD);
    }};

    private static final int RECORD_AVERGAE_COLUMN_NUMBER = 16;

    private final List<Column> columns;

    private int byteSize;

    // 首先是Record本身需要的内存
    private int memorySize = ClassSize.DefaultRecordHead;

    public DefaultRecord(int sizeCapacity) {
        this.columns = new ArrayList<>(sizeCapacity);
    }

    public DefaultRecord() {
        this(RECORD_AVERGAE_COLUMN_NUMBER);
    }

    @Override
    public void addColumn(Column column) {
        columns.add(column);
        incrByteSize(column);
    }

    @Override
    public void addColumns(List<Column> columns) {
        if (CollectionUtils.isEmpty(columns)) {
            return;
        }
        this.columns.addAll(columns);
        incrByteSize(columns);
    }

    @Override
    public Column getColumn(int i) {
        if (i < 0 || i >= columns.size()) {
            return null;
        }
        return columns.get(i);
    }

    @Override
    public void setColumn(int i, final Column column) {
        if (i >= columns.size()) {
            expandCapacity(i + 1);
        }
        decrByteSize(getColumn(i));
        this.columns.set(i, column);
        incrByteSize(getColumn(i));
    }

    @Override
    public String toString() {
        Map<String, Object> json = new HashMap<>();
        json.put("size", this.getColumnNumber());
        json.put("data", this.columns);
        return JSON.toJSONString(json);
    }

    @Override
    public int getColumnNumber() {
        return this.columns.size();
    }

    @Override
    public int getByteSize() {
        return byteSize;
    }

    @Override
    public int getMemorySize() {
        return memorySize;
    }

    @Override
    public List<Column> getColumnList() {
        return columns;
    }

    private void decrByteSize(final Column column) {
        if (null == column) {
            return;
        }

        byteSize -= column.getByteSize();

        //内存的占用是column对象的头 再加实际大小
        memorySize = memorySize - ClassSize.ColumnHead - column.getByteSize();
    }

    private void incrByteSize(final Column column) {
        if (null == column) {
            return;
        }

        byteSize += column.getByteSize();

        //内存的占用是column对象的头 再加实际大小
        memorySize = memorySize + ClassSize.ColumnHead + column.getByteSize();
    }

    private void incrByteSize(final List<Column> columns) {
        if (CollectionUtils.isEmpty(columns)) {
            return;
        }

        int sum = columns.stream().mapToInt(Column::getByteSize).sum();

        byteSize += sum;

        //内存的占用是column对象的头 再加实际大小
        memorySize = memorySize + (ClassSize.ColumnHead * columns.size()) + sum;
    }

    private void expandCapacity(int totalSize) {
        if (totalSize <= 0) {
            return;
        }

        int needToExpand = totalSize - columns.size();
        while (needToExpand-- > 0) {
            this.columns.add(null);
        }
    }
}