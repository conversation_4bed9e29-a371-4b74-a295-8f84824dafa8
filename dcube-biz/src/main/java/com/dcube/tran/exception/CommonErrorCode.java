package com.dcube.tran.exception;

public enum CommonErrorCode implements ErrorCode {

    CONVERT_NOT_SUPPORT("data-tran-01", "数据类型转换错误 ."),
    UK_NOT_SUPPORT("data-tran-02", "唯一性验证错误 ."),

    READ_NOT_SUPPORT("database-read-01", "解析数据库类型错误 ."),
    READ_SQL_QUERY("database-read-02", "执行SQL查询错误 ."),
    READ_CUBE_QUERY("database-read-03", "查询维度错误 ."),

    WRITER_NOT_SUPPORT("database-writer-01", "转换数据库类型错误 ."),

    CONVERT_OVER_FLOW("common-02", "同步数据出现业务脏数据情况，数据类型转换溢出 ."),

    RUNTIME_ERROR("runtime-error-01", "运行时错误 .");

    private final String code;

    private final String describe;

    CommonErrorCode(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.describe;
    }

    @Override
    public String toString() {
        return String.format("Code:[%s], Describe:[%s]", this.code, this.describe);
    }
}
