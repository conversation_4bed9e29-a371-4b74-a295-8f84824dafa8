package com.dcube.workflow.runtime.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.workflow.runtime.constants.WfRtConstants;
import com.dcube.workflow.runtime.constants.enums.WfEventTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfOperateTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.domain.WfVariable;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.dcube.workflow.runtime.listener.event.WfEvent;
import com.dcube.workflow.runtime.listener.event.WfEventSource;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.service.IWfVariableService;
import com.dcube.workflow.runtime.strategy.ApproveStrategy;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 退回
 */
@Service("RETURN")
public class ReturnStrategyImpl implements ApproveStrategy {

    @Autowired
    private IWfInstanceService wfInstanceService;
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private IWfVariableService wfVariableService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult approve(WfTaskDTO wfTaskDto) {
        WfTask wfTask = wfTaskService.getById(wfTaskDto.getId());
        Assert.notNull(wfTask, "未查询到任务");
        WfInstance wfInstance = wfInstanceService.getById(wfTask.getInstanceId());
        Assert.notNull(wfInstance, "未查询到流程实例");
        if (wfInstance.getInstanceState() != WfInstanceStateEnums.RUNNING) {
            throw new WfRuntimeException("流程实例状态不为运行中，无法退回");
        }
        wfTask.setCentralizeDimDefineId(wfTaskDto.getCentralizeDimDefineId());
        wfTask.setOperateType(WfOperateTypeEnums.RETURN);
        switch (wfTask.getFlowType()) {
            case CUBE:
                handleCubeTask(wfTask);
                break;
        }

        // 发布退回事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfTask, wfEventSource);
        wfEventSource.setEventType(WfEventTypeEnums.TASK_RETURN);
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));

        return AjaxResult.success("退回成功");
    }

    private void handleCubeTask(WfTask wfTask) {
        // 归口节点-退回：“待处理节点”状态必须是“待审批”
        // 其它节点-退回：“待处理节点”状态是“待审批”或“已审批”
        if (wfTask.getCentralizeDimDefineId() == null) {
            if (!(wfTask.getTaskState() == WfTaskStateEnums.PRE_APPROVE || wfTask.getTaskState() == WfTaskStateEnums.APPROVED)) {
                throw new WfRuntimeException("该待处理节点状态不为待审批或已审批，无法退回！");
            }
            // 非归口退回修改原来任务为待提交
            wfTask.setOperateUserId(String.valueOf(SecurityUtils.getUserId()));
            // 退回成待提交
            wfTask.setTaskState(WfTaskStateEnums.PRE_SUBMIT);
            wfTask.setTaskDone("0");
//        wfTask.setApproveTime(new Date());
//        wfTask.setCost(DateUtil.betweenMs(wfTask.getApproveTime(), wfTask.getCreateTime()));
            wfTaskService.updateById(wfTask);
        } else {
            if (wfTask.getTaskState() != WfTaskStateEnums.PRE_APPROVE) {
                throw new WfRuntimeException("该待处理节点状态不为待审批，无法退回！");
            }
        }

        // 流程变量
        LambdaUpdateWrapper<WfVariable> wfVariableLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        // 归口节点审批
        if (wfTask.getCentralizeDimDefineId() == null) {
            wfVariableLambdaUpdateWrapper.isNull(WfVariable::getCentralizeId)
                    .set(WfVariable::getVariableVal, wfTask.getTaskState().name());
        } else {
            wfVariableLambdaUpdateWrapper.eq(WfVariable::getCentralizeId, wfTask.getCentralizeDimDefineId())
                    .set(WfVariable::getVariableVal, WfTaskStateEnums.PRE_HANDLE.name());
        }
        wfVariableLambdaUpdateWrapper.eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                .eq(WfVariable::getNodeId, wfTask.getNodeId())
                .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE);
        wfVariableService.update(wfVariableLambdaUpdateWrapper);
    }

}
