package com.dcube.workflow.define.flowchart;

import com.dcube.workflow.define.constants.enums.FlowNodeTypeEnums;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@NoArgsConstructor
@Data
public class FlowChart implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<NodeDTO> nodes = Collections.emptyList();
    private List<EdgeDTO> edges = Collections.emptyList();

    @NoArgsConstructor
    @Data
    public static class NodeDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String id;
        private FlowNodeTypeEnums type;
        //        private Integer x;
//        private Integer y;
        private PropertiesDTO properties;
        private TextDTO text;

        @NoArgsConstructor
        @Data
        public static class PropertiesDTO implements Serializable {
            private static final long serialVersionUID = 1L;

            private String text;

            private String roleId;

            private List<Long> roleIds;

            private String roleName;

            private String unitColumnCode;

            private String unitColumnName;

        }

        @NoArgsConstructor
        @Data
        public static class TextDTO implements Serializable {
            private static final long serialVersionUID = 1L;

            //            private Integer x;
//            private Integer y;
            private String value;
        }
    }

    @NoArgsConstructor
    @Data
    public static class EdgeDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String id;
        private String type;
        private String sourceNodeId;
        private String targetNodeId;
        //        private StartPointDTO startPoint;
//        private EndPointDTO endPoint;
        private PropertiesDTO properties;
        private TextDTO text;
//        private List<PointsListDTO> pointsList = Collections.emptyList();

//        @NoArgsConstructor
//        @Data
//        public static class StartPointDTO implements Serializable {
//            private static final long serialVersionUID = 1L;
//
//            private Integer x;
//            private Integer y;
//        }
//
//        @NoArgsConstructor
//        @Data
//        public static class EndPointDTO implements Serializable {
//            private static final long serialVersionUID = 1L;
//
//            private Integer x;
//            private Integer y;
//        }

        @NoArgsConstructor
        @Data
        public static class PropertiesDTO implements Serializable {
            private static final long serialVersionUID = 1L;

            private String text;
            private String type;
            private String visibleCondition;
            private String executeOperation;
        }

        @NoArgsConstructor
        @Data
        public static class TextDTO implements Serializable {
            private static final long serialVersionUID = 1L;

            //            private Integer x;
//            private Integer y;
            private String value;
        }

//        @NoArgsConstructor
//        @Data
//        public static class PointsListDTO implements Serializable {
//            private static final long serialVersionUID = 1L;
//
//            private Integer x;
//            private Integer y;
//        }
    }
}
