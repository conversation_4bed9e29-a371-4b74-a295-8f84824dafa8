package com.dcube.workflow.runtime.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode
@ToString
public class SubmitInfoEdgeVO {
    private String id;
    private String type;
    private String sourceNodeId;
    private String targetNodeId;
    //        private StartPointDTO startPoint;
//        private EndPointDTO endPoint;
//    private FlowChart.EdgeDTO.PropertiesDTO properties;
    private String text;
    private String nextNodeUser;

}
