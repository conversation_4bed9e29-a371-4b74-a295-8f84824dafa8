package com.dcube.workflow.runtime.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 流程运行变量 cube_wf_variable
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
@TableName("cube_wf_variable")
@EqualsAndHashCode(callSuper = false)
@ToString
public class WfVariable extends Model<WfVariable> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long instanceId;

    /**
     * 所属节点id
     */
    @Schema(description = "所属节点id")
    private String nodeId;

    /**
     * 归口id
     */
    @Schema(description = "归口id")
    private String centralizeId;

    /**
     * 变量key
     */
    @Schema(description = "变量key")
    private String variableKey;

    /**
     * 变量值
     */
    @Schema(description = "变量值")
    private String variableVal;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;

}
