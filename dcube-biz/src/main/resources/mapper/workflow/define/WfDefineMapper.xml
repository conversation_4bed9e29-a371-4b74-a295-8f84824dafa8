<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.workflow.define.mapper.WfDefineMapper">

    <resultMap type="WfDefine" id="CubeWfDefineResult">
        <result property="id" column="id"/>
        <result property="tableId" column="table_id"/>
        <result property="flowType" column="flow_type"/>
        <result property="flowName" column="flow_name"/>
        <result property="flowStateColumnName" column="flow_state_column_name"/>
        <result property="flowStateColumnCode" column="flow_state_column_code"/>
        <result property="batchSubmit" column="batch_submit"/>
        <result property="parentId" column="parent_id"/>
        <result property="type" column="type"/>
        <result property="ancestors" column="ancestors"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="flowchartJson" column="flowchart_json"/>
    </resultMap>

    <sql id="selectCubeWfDefineVo">
        select id,
               table_id,
               flow_type,
               flow_name,
               flow_state_column_name,
               flow_state_column_code,
               batch_submit,
               parent_id,
               type,
               ancestors,
               create_by,
               create_time,
               update_by,
               update_time,
               flowchart_json
        from cube_wf_define
        where del_flag = '0'
    </sql>

    <select id="selectCubeWfDefineList" parameterType="WfDefine" resultMap="CubeWfDefineResult">
        select d.id,
        d.table_id,
        d.flow_type,
        d.flow_name,
        d.flow_state_column_name,
        d.flow_state_column_code,
        d.batch_submit,
        d.parent_id,
        d.type,
        d.ancestors,
        d.create_by,
        d.create_time,
        d.update_by,
        d.update_time
        from cube_wf_define d
        <where>
            d.del_flag='0'
            <if test="tableId != null ">and d.table_id = #{tableId}</if>
            <if test="flowType != null  and flowType != ''">and d.flow_type = #{flowType}</if>
            <if test="flowName != null  and flowName != ''">and d.flow_name like concat('%', #{flowName}, '%')</if>
            <if test="parentId != null ">and d.parent_id = #{parentId}</if>
            <if test="type != null  and type != ''">and d.type = #{type}</if>
            <if test="ancestors != null  and ancestors != ''">and d.ancestors = #{ancestors}</if>
        </where>
    </select>

    <select id="selectCubeWfDefineById" parameterType="Long" resultMap="CubeWfDefineResult">
        <include refid="selectCubeWfDefineVo"/>
        where id = #{id} and del_flag='0'
    </select>

    <insert id="insertCubeWfDefine" parameterType="WfDefine" useGeneratedKeys="true" keyProperty="id">
        insert into cube_wf_define
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableId != null">table_id,</if>
            <if test="flowType != null">flow_type,</if>
            <if test="flowName != null">flow_name,</if>
            <if test="flowStateColumnName != null">flow_state_column_name,</if>
            <if test="flowStateColumnCode != null">flow_state_column_code,</if>
            <if test="batchSubmit != null">batch_submit,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="type != null">type,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableId != null">#{tableId},</if>
            <if test="flowType != null">#{flowType},</if>
            <if test="flowName != null">#{flowName},</if>
            <if test="flowStateColumnName != null">#{flow_state_column_name},</if>
            <if test="flowStateColumnCode != null">#{flow_state_column_code},</if>
            <if test="batchSubmit != null">#{batch_submit},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="type != null">#{type},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCubeWfDefine" parameterType="WfDefine">
        update cube_wf_define
        <trim prefix="SET" suffixOverrides=",">
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="flowType != null">flow_type = #{flowType},</if>
            <if test="flowName != null">flow_name = #{flowName},</if>
            <if test="flowStateColumnName != null">flow_name = #{flow_state_column_name},</if>
            <if test="flowStateColumnCode != null">flow_name = #{flow_state_column_code},</if>
            <if test="batchSubmit != null">flow_name = #{batch_submit},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCubeWfDefineById" parameterType="Long">
        update cube_wf_define
        set del_flag='0'
        where id = #{id}
    </delete>

    <delete id="deleteCubeWfDefineByIds" parameterType="String">
        update cube_wf_define
        set del_flag='0'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>