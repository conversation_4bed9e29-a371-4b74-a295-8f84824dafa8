package com.dcube.workflow.define.controller;

import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.define.dto.WfDimDefineDetailSaveBatchDTO;
import com.dcube.workflow.define.dto.WfDimDefineDetailSaveDTO;
import com.dcube.workflow.define.service.IWfDimDefineDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

@Tag(name = "DCUBE-多维流程定义", description = "DCUBE-多维流程定义")
@RestController
@RequestMapping("/workflow/dimDefineDetail")
@PreAuthorize("@ss.hasPermi('approve-process:list')")
public class WfDimDefineDetailController extends BaseController {
    @Autowired
    private IWfDimDefineDetailService wfDimDefineDetailService;

    /**
     * 批量保存
     */
    @Operation(summary = "批量保存")
    @Log(title = "批量保存", businessType = BusinessType.INSERT)
    @PostMapping("/saveBatch")
    public AjaxResult saveBatch(@RequestBody WfDimDefineDetailSaveBatchDTO wfDimDefineDetailSaveBatchDto) {
        Assert.notNull(wfDimDefineDetailSaveBatchDto.getDefineId(), "流程定义id不能为空");
        wfDimDefineDetailService.saveBatch(wfDimDefineDetailSaveBatchDto);
        return AjaxResult.success("保存成功");
    }

    /**
     * 保存
     */
    @Operation(summary = "保存")
    @Log(title = "保存", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody WfDimDefineDetailSaveDTO wfDimDefineDetailSaveDto) {
        Assert.notNull(wfDimDefineDetailSaveDto.getDefineId(), "流程定义id不能为空");
        wfDimDefineDetailService.save(wfDimDefineDetailSaveDto);
        return AjaxResult.success("保存成功");
    }

    /**
     * 保存归口节点
     */
    @Operation(summary = "保存归口节点")
    @Log(title = "保存归口节点", businessType = BusinessType.INSERT)
    @PostMapping("/saveCentralize")
    public AjaxResult saveCentralize(@RequestBody WfDimDefineCentralize wfDimDefineCentralize) {
        wfDimDefineDetailService.saveCentralize(wfDimDefineCentralize);
        return AjaxResult.success("保存成功");
    }

    /**
     * 查询归口节点
     */
    @Operation(summary = "查询归口节点")
    @Log(title = "查询归口节点", businessType = BusinessType.INSERT)
    @PostMapping("/getCentralize")
    public AjaxResult getCentralize(@RequestParam("dimDefineDetailId") Long dimDefineDetailId) {
        return AjaxResult.success(wfDimDefineDetailService.getCentralize(dimDefineDetailId));
    }
}
