package com.dcube.workflow.runtime.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class WfGetNextEdgeDTO extends BaseDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "二维表id")
    private Integer tableId;
    @Schema(description = "任务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;
    @Schema(description = "节点id")
    private String nodeId;
    @Schema(description = "数据id")
    private String dataId;

    /**
     * 流程实例类型
     */
    @Schema(description = "流程实例类型")
    private WfInstanceTypeEnums flowType = WfInstanceTypeEnums.TABLE;

}
