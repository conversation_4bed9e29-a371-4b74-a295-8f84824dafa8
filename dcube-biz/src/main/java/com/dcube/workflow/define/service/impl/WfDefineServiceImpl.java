package com.dcube.workflow.define.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DimDirectory;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.domain.DimTable;
import com.dcube.biz.domain.Table;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.IDimDirectoryService;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.biz.service.IDimTableService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.dcube.system.service.ISysUserService;
import com.dcube.workflow.define.constants.enums.WfDefineFlowTypeEnums;
import com.dcube.workflow.define.constants.enums.WfDefineTypeEnums;
import com.dcube.workflow.define.domain.WfDefine;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.define.domain.WfDimDefineDetail;
import com.dcube.workflow.define.exception.WfDefineException;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.define.mapper.WfDefineMapper;
import com.dcube.workflow.define.service.IWfDefineService;
import com.dcube.workflow.define.service.IWfDimDefineCentralizeService;
import com.dcube.workflow.define.service.IWfDimDefineDetailService;
import com.dcube.workflow.define.utils.FlowChartUtils;
import com.dcube.workflow.define.vo.WfDimDefineDetailVO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程定义Service业务层处理
 *
 * @date 2024-02-25
 */
@Service
public class WfDefineServiceImpl extends ServiceImpl<WfDefineMapper, WfDefine> implements IWfDefineService {

    @Autowired
    @Lazy
    private ITableService tableService;
    @Autowired
    @Lazy
    private IDimTableService dimTableService;
    @Autowired
    private IWfDimDefineDetailService wfDimDefineDetailService;
    @Autowired
    private IWfDimDefineCentralizeService wfDimDefineCentralizeService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IDimDirectoryService dimDirectoryService;
    @Autowired
    private IDimInstanceService dimInstanceService;

    /**
     * 查询流程定义
     *
     * @param id 流程定义主键
     * @return 流程定义
     */
    @Override
    public WfDefine selectCubeWfDefineById(Long id) {
        WfDefine wfDefine = this.getById(id);
        if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
            FlowChart flowChart = new FlowChart();
            wfDefine.setFlowchartJson(JSON.toJSONString(flowChart));
        }
        return wfDefine;
    }

    /**
     * 查询流程定义列表
     *
     * @param wfDefine 流程定义
     * @return 流程定义
     */
    @Override
    public List<WfDefine> selectCubeWfDefineList(WfDefine wfDefine) {
        List<WfDefine> wfDefines = this.baseMapper.selectCubeWfDefineList(wfDefine);
        if (CollectionUtils.isEmpty(wfDefines)) {
            return Collections.emptyList();
        }
        Map<WfDefineFlowTypeEnums, List<WfDefine>> flowTypeEnumsMap = wfDefines.stream()
                .filter(define -> define.getType() == WfDefineTypeEnums.FLOW)
                .collect(Collectors.groupingBy(WfDefine::getFlowType));
        Map<WfDefineFlowTypeEnums, Map<Long, String>> flowTypeTableNameMap = Maps.newHashMapWithExpectedSize(2);
        for (Map.Entry<WfDefineFlowTypeEnums, List<WfDefine>> entry : flowTypeEnumsMap.entrySet()) {
            if (entry.getKey() == WfDefineFlowTypeEnums.TABLE) {
                Set<Long> tableIds = StreamUtils.toSet(entry.getValue(), WfDefine::getTableId);
                List<Table> tables = tableService.listByIds(tableIds);
                flowTypeTableNameMap.put(entry.getKey(), StreamUtils.toMap(tables, table -> Long.valueOf(table.getId()), Table::getTableName));
            } else if (entry.getKey() == WfDefineFlowTypeEnums.CUBE) {
                Set<Long> tableIds = StreamUtils.toSet(entry.getValue(), WfDefine::getTableId);
                List<DimTable> tables = dimTableService.listByIds(tableIds);
                flowTypeTableNameMap.put(entry.getKey(), StreamUtils.toMap(tables, table -> Long.valueOf(table.getId()), DimTable::getTableName));
            }
        }
        wfDefines.forEach(define -> define.setTableName(MapUtils.getString(MapUtils.getObject(flowTypeTableNameMap, define.getFlowType()), define.getTableId())));
        return wfDefines;
    }

    /**
     * 新增流程定义
     *
     * @param wfDefine 流程定义
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertCubeWfDefine(WfDefine wfDefine) {
        if (wfDefine.getParentId() != null) {
            WfDefine parent = this.getById(wfDefine.getParentId());
            if (parent != null) {
                wfDefine.setAncestors(parent.getAncestors() + "," + wfDefine.getParentId());
            }
        }
        boolean save = this.save(wfDefine);
        //
        if (wfDefine.getFlowType() == WfDefineFlowTypeEnums.TABLE) {
            Table table = tableService.getById(wfDefine.getTableId());
            Assert.notNull(table, "未找到二维表");
            List<TableMetaJson> tableMetaJson = JSON.parseArray(table.getTableMeta(), TableMetaJson.class);
            int wfStateColumnIndex = 0;
            boolean wfStateColumnFlag = false;
            if (CollectionUtils.isNotEmpty(tableMetaJson)) {
                for (TableMetaJson tableMeta : tableMetaJson) {
                    if (StringUtils.equals(tableMeta.getCode(), wfDefine.getFlowStateColumnCode())) {
                        wfStateColumnFlag = true;
                        break;
                    }
                    wfStateColumnIndex++;
                }
            }
            if (!wfStateColumnFlag) {
                throw new WfRuntimeException("未找到二维表流程状态列");
            }
            Object[][] tableData = MemorySchema.getDatabase().getTableData(table.getMemTableName());
            if (ArrayUtils.isNotEmpty(tableData)) {
                for (Object[] data : tableData) {
                    data[wfStateColumnIndex] = BizConstants.WfState.PRE_SUBMIT;
                }
            }
        }
        return save;
    }

    /**
     * 修改流程定义
     *
     * @param wfDefine 流程定义
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCubeWfDefine(WfDefine wfDefine) {
        if (StringUtils.isNotEmpty(wfDefine.getFlowchartJson())) {
            FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
            if (flowChart != null) {
                List<FlowChart.NodeDTO> startNodes = FlowChartUtils.getStartNodes(flowChart);
                if (CollectionUtils.isEmpty(startNodes)) {
                    throw new WfDefineException("请添加开始节点");
                } else if (startNodes.size() > 1) {
                    throw new WfDefineException("请勿添加多个开始节点");
                }
            }
        }

        return this.updateById(wfDefine);
    }

    /**
     * 批量删除流程定义
     *
     * @param ids 需要删除的流程定义主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCubeWfDefineByIds(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        List<WfDefine> wfDefines = this.listByIds(idList);
        if (CollectionUtils.isNotEmpty(wfDefines)) {
            QueryWrapper<WfDefine> queryWrapper = new QueryWrapper<>();
            for (WfDefine wfDefine : wfDefines) {
                queryWrapper.eq("id", wfDefine.getId()).or().likeRight("ancestors", wfDefine.getAncestors() + "," + wfDefine.getId());
            }

            int delete = this.baseMapper.delete(queryWrapper);
            Set<Long> dimDefineIds = wfDefines.stream()
                    .filter(wfDefine -> wfDefine.getFlowType() == WfDefineFlowTypeEnums.CUBE)
                    .map(WfDefine::getId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(dimDefineIds)) {
                List<WfDimDefineDetail> list = wfDimDefineDetailService.list(Wrappers.<WfDimDefineDetail>lambdaQuery().in(WfDimDefineDetail::getDefineId, dimDefineIds));
                if (CollectionUtils.isNotEmpty(list)) {
                    wfDimDefineDetailService.removeBatchByIds(list);
                    Set<Long> wfDimDefineDetailIds = StreamUtils.toSet(list, WfDimDefineDetail::getId);
                    wfDimDefineCentralizeService.remove(Wrappers.<WfDimDefineCentralize>lambdaQuery().in(WfDimDefineCentralize::getDimDefineDetailId, wfDimDefineDetailIds));
                }
            }
            return delete;
        }

        return 0;
    }

    /**
     * 删除流程定义信息
     *
     * @param id 流程定义主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCubeWfDefineById(Long id) {
        return this.baseMapper.deleteCubeWfDefineById(id);
    }

    @Override
    public List<Table> get2dTable(TableListQuery query) {
        QueryWrapper<WfDefine> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("table_id");
        queryWrapper.eq("type", WfDefineTypeEnums.FLOW);
        List<WfDefine> list = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            query.setFilterTableIdList(list.stream().map(WfDefine::getTableId).map(String::valueOf).collect(Collectors.toList()));
        }
        return tableService.list(query);
    }

    @Override
    public List<TableMetaJson> get2dTableMeta(Integer tableId) {
        Table table = tableService.getById(tableId);
        if (StringUtils.isNotBlank(table.getTableMeta())) {
            List<TableMetaJson> tableMetaJson = JSON.parseArray(table.getTableMeta(), TableMetaJson.class);
            if (CollectionUtils.isNotEmpty(tableMetaJson)) {
                tableMetaJson = tableMetaJson.stream().filter(metaJson -> {
                    String columnType;
                    if (metaJson.getDataFormat() != null) {
                        columnType = metaJson.getDataFormat().getStorageType();
                    } else if (StringUtils.isNotEmpty(metaJson.getNewColumnType())) {
                        columnType = metaJson.getNewColumnType();
                    } else {
                        columnType = "VARCHAR";
                    }
                    Class clazz = MemGridUtils.getColumnType(columnType);
                    return String.class == clazz;
                }).collect(Collectors.toList());
                return tableMetaJson;
            }
        }

        return Collections.emptyList();
    }

    @Override
    public WfDefine getByTableId(Integer tableId) {
        return getByTableId(tableId, false);
    }

    @Override
    public WfDefine getByTableId(Integer tableId, boolean needFlowChart) {
        QueryWrapper<WfDefine> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id",
                "table_id",
                "flow_type",
                "flow_name",
                "flow_state_column_name",
                "flow_state_column_code",
                "batch_submit",
                "parent_id",
                "type",
                "ancestors",
                "create_by",
                "create_time",
                "update_by",
                "update_time");
        queryWrapper.select(needFlowChart, queryWrapper.getSqlSelect() + ",flowchart_json");
        queryWrapper.eq("table_id", tableId);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<FlowChart.NodeDTO> getNextNodes(Long defineId, String nodeId) {
        WfDefine wfDefine = getById(defineId);
        if (wfDefine == null) {
            throw new WfDefineException("未找到流程定义");
        }
        FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
        if (StringUtils.isNotEmpty(nodeId)) {
            return FlowChartUtils.getNextNodes(flowChart, nodeId);
        } else {
            return Collections.singletonList(FlowChartUtils.getStartNode(flowChart));
        }
    }

    @Override
    public FlowChart.NodeDTO getStartNode(Long defineId) {
        WfDefine wfDefine = getById(defineId);
        if (wfDefine == null) {
            throw new WfDefineException("未找到流程定义");
        }
        FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
        return FlowChartUtils.getStartNode(flowChart);
    }

    @Override
    public List<TableMetaJson> getUnitTableMeta(Integer id) {
        WfDefine wfDefine = this.getById(id);
        Assert.notNull(wfDefine, "未查询到流程定义");
        Table table = tableService.getById(wfDefine.getTableId());
        Assert.notNull(table, "未查询到二维表");
        if (StringUtils.isNotBlank(table.getTableMeta())) {
            List<TableMetaJson> tableMetaJson = JSON.parseArray(table.getTableMeta(), TableMetaJson.class);
            if (CollectionUtils.isNotEmpty(tableMetaJson)) {
                tableMetaJson = tableMetaJson.stream()
                        .filter(metaJson -> {
                            String columnType = "VARCHAR";
                            // 为兼容数据格式 优先赋值存储格式
                            if (metaJson.getDataFormat() != null) {
                                columnType = metaJson.getDataFormat().getStorageType();
                            } else if (StringUtils.isNotEmpty(metaJson.getNewColumnType())) {
                                columnType = metaJson.getNewColumnType();
                            }

                            Class cl = MemGridUtils.getColumnType(columnType);
                            return String.class == cl;
                        })
                        .collect(Collectors.toList());
                return tableMetaJson;
            }
        }

        return Collections.emptyList();
    }

    @Override
    public List<DimTable> getCubeTableList(DimTableListQuery query) {
        return dimTableService.list(query);
    }

    @Override
    public List<DimDirectory> getDimListByDimTableId(Integer dimTableId) {
        DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(dimTableId);
        Assert.notNull(dimTableInfoVo, "未查询到多维表");
        return dimTableInfoVo.getDimList();
    }

    @Override
    public List<WfDimDefineDetailVO> getByDefineId(Long defineId, String dimName) {
        return getByDefineId(defineId, dimName, true, null);
    }

    @Override
    public List<WfDimDefineDetailVO> getByDefineId(Long defineId, Set<String> filterDimIds) {
        return getByDefineId(defineId, null, true, filterDimIds);
    }

    @Override
    public List<WfDimDefineDetailVO> getByDefineId(Long defineId, String dimName, boolean withUserName, Set<String> filterDimIds) {
        WfDefine wfDefine = this.getById(defineId);
        Assert.notNull(wfDefine, "未查询到流程定义");
        String flowStateColumnCode = wfDefine.getFlowStateColumnCode();
        List<DimInstance> dimInstanceList = dimInstanceService.list(
                Wrappers.<DimInstance>lambdaQuery()
                        .eq(DimInstance::getDimDirectoryId, flowStateColumnCode)
                        .like(StringUtils.isNotEmpty(dimName), DimInstance::getDimName, dimName)
                        .in(CollectionUtils.isNotEmpty(filterDimIds), DimInstance::getId, filterDimIds)
        );
        if (CollectionUtils.isEmpty(dimInstanceList)) {
            return Collections.emptyList();
        }
        List<WfDimDefineDetail> list = wfDimDefineDetailService.list(Wrappers.<WfDimDefineDetail>lambdaQuery().eq(WfDimDefineDetail::getDefineId, defineId));
        Map<Long, WfDimDefineDetail> wfDimDefineDetailMap = StreamUtils.toMap(list, WfDimDefineDetail::getDimId, Function.identity());
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> userIds = new HashSet<>();
            Set<Integer> dimIds = new HashSet<>();
            for (WfDimDefineDetail wfDimDefineDetail : list) {
                if (StringUtils.isNotEmpty(wfDimDefineDetail.getUserId())) {
                    if (StringUtils.contains(wfDimDefineDetail.getUserId(), ",")) {
                        userIds.addAll(Arrays.stream(wfDimDefineDetail.getUserId().split(",")).map(Long::valueOf).collect(Collectors.toSet()));
                    } else {
                        userIds.add(Long.valueOf(wfDimDefineDetail.getUserId()));
                    }
                }
                if (StringUtils.isNotEmpty(wfDimDefineDetail.getCentralizedDimId())) {
                    dimIds.add(Integer.valueOf(wfDimDefineDetail.getCentralizedDimId()));
                }
            }
            Map<Long, String> userIdMap;
            if (withUserName && CollectionUtils.isNotEmpty(userIds)) {
                List<SysUser> sysUsers = sysUserService.listByIds(userIds);
                userIdMap = StreamUtils.toMap(sysUsers, SysUser::getUserId, v -> v.getNickName() + "(" + v.getUserName() + ")");
            } else {
                userIdMap = Collections.emptyMap();
            }
            Map<Integer, String> dimDirectoryIdMap;
            if (CollectionUtils.isNotEmpty(dimIds)) {
                List<DimDirectory> dimDirectories = dimDirectoryService.listByIds(dimIds);
                dimDirectoryIdMap = StreamUtils.toMap(dimDirectories, DimDirectory::getId, DimDirectory::getDimDirectoryName);
            } else {
                dimDirectoryIdMap = Collections.emptyMap();
            }
            list.forEach(wfDimDefineDetail -> {
                if (StringUtils.isNotEmpty(wfDimDefineDetail.getUserId())) {
                    if (StringUtils.contains(wfDimDefineDetail.getUserId(), ",")) {
                        wfDimDefineDetail.setUserName(Arrays.stream(wfDimDefineDetail.getUserId().split(",")).map(v -> MapUtils.getObject(userIdMap, Long.valueOf(v))).filter(StringUtils::isNotEmpty).collect(Collectors.joining(",")));
                    } else {
                        wfDimDefineDetail.setUserName(MapUtils.getObject(userIdMap, Long.valueOf(wfDimDefineDetail.getUserId())));
                    }
                }
                if (StringUtils.isNotEmpty(wfDimDefineDetail.getCentralizedDimId())) {
                    wfDimDefineDetail.setCentralizedDimName(MapUtils.getString(dimDirectoryIdMap, Integer.valueOf(wfDimDefineDetail.getCentralizedDimId())));
                }
            });
        }
        return dimInstanceList.stream()
                .map(dimInstance -> {
                    WfDimDefineDetailVO wfDimDefineDetailVO = new WfDimDefineDetailVO();
                    Long dimId = Long.valueOf(dimInstance.getId());
                    wfDimDefineDetailVO.setDimId(dimId);
                    wfDimDefineDetailVO.setDimName(dimInstance.getDimName());
                    wfDimDefineDetailVO.setAncestors(dimInstance.getAncestors());
                    wfDimDefineDetailVO.setParentId(Long.valueOf(dimInstance.getParentId()));
                    wfDimDefineDetailVO.setIsLeaf(dimInstance.getIsLeaf());
                    WfDimDefineDetail defineDetail = MapUtils.getObject(wfDimDefineDetailMap, dimId);
                    wfDimDefineDetailVO.setDefineId(defineId);
                    if (defineDetail != null) {
                        wfDimDefineDetailVO.setId(defineDetail.getId());
                        wfDimDefineDetailVO.setUserId(defineDetail.getUserId());
                        if (StringUtils.isNotEmpty(wfDimDefineDetailVO.getUserId())) {
                            wfDimDefineDetailVO.setUserIds(Arrays.asList(wfDimDefineDetailVO.getUserId().split(",")));
                        }
                        wfDimDefineDetailVO.setUserName(defineDetail.getUserName());
                        wfDimDefineDetailVO.setCentralizedDimId(defineDetail.getCentralizedDimId());
                        wfDimDefineDetailVO.setCentralizedDimName(defineDetail.getCentralizedDimName());
                    }
                    return wfDimDefineDetailVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWfDefine(List<Integer> idList, WfDefineFlowTypeEnums wfDefineFlowTypeEnums) {
        this.remove(Wrappers.<WfDefine>lambdaQuery().in(WfDefine::getTableId, idList).eq(WfDefine::getFlowType, wfDefineFlowTypeEnums));
    }

}
