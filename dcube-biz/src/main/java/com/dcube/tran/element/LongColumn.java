package com.dcube.tran.element;

import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class LongColumn extends Column {

    private static final Long ZERO = 0L;

    /**
     * 从整形字符串表示转为LongColumn，支持Java科学计数法
     *
     * NOTE: <br> 如果data为浮点类型的字符串表示，数据将会失真，请使用DoubleColumn对接浮点字符串
     */
    public LongColumn(final String data) {
        super(null, Column.Type.LONG, 0);
        if (null == data) {
            return;
        }

        try {
            super.setRawData(Long.valueOf(data));

            // 当 rawData 为[0-127]时，rawData.bitLength() < 8，导致其 byteSize = 0，简单起见，直接认为其长度为 data.length()
            // super.setByteSize(rawData.bitLength() / 8);
            super.setByteSize(data.length());
        } catch (Exception e) {
            throw DataTranException.asDataTranException(
                    CommonErrorCode.CONVERT_NOT_SUPPORT,
                    String.format("String[%s]不能转为Long .", data));
        }
    }

    public LongColumn(Integer data) {
        this(null == data ? null : Long.valueOf(data));
    }

    public LongColumn(Long data) {
        this(data, null == data ? 0 : 8);
    }

    private LongColumn(Long data, int byteSize) {
        super(data, Column.Type.LONG, byteSize);
    }

    public LongColumn() {
        this((Long) null);
    }

    @Override
    public BigInteger asBigInteger() {
        if (null == this.getRawData()) {
            return null;
        }

        return new BigInteger(this.asString());
    }

    @Override
    public Long asLong() {
        if (null == this.getRawData()) {
            return null;
        }
        return (Long) this.getRawData();
    }

    @Override
    public Double asDouble() {
        if (null == this.getRawData()) {
            return null;
        }
        return ((Long) this.getRawData()).doubleValue();
    }

    @Override
    public Boolean asBoolean() {
        if (null == this.getRawData()) {
            return null;
        }

        return ((Long) this.getRawData()).compareTo(ZERO) != 0;
    }

    @Override
    public BigDecimal asBigDecimal() {
        if (null == this.getRawData()) {
            return null;
        }

        return new BigDecimal(this.asString());
    }

    @Override
    public String asString() {
        if (null == this.getRawData()) {
            return null;
        }
        return ((Long) this.getRawData()).toString();
    }

    @Override
    public Date asDate() {
        if (null == this.getRawData()) {
            return null;
        }
        return new Date(this.asLong());
    }

    @Override
    public byte[] asBytes() {
        throw DataTranException.asDataTranException(
                CommonErrorCode.CONVERT_NOT_SUPPORT, "Long类型不能转为Bytes .");
    }

}
