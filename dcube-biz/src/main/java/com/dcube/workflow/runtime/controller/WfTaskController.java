package com.dcube.workflow.runtime.controller;

import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.dto.WfGetNextEdgeDTO;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.strategy.ApproveStrategy;
import com.dcube.workflow.runtime.strategy.factory.ApproveStrategyFactory;
import com.dcube.workflow.runtime.vo.WfTaskVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 流程任务Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@RestController
@RequestMapping("/workflow/runtime/task")
@Tag(name = "DCUBE-流程任务", description = "DCUBE-流程任务")
public class WfTaskController extends BaseController {
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private ApproveStrategyFactory approveStrategyFactory;

    /**
     * 查询流程任务列表
     */
    @Operation(summary = "查询流程任务列表")
    @GetMapping("/list")
    public TableDataInfo list(WfTaskDTO wfTask) {
        startPage();
        List<WfTaskVO> list = wfTaskService.selectCubeWfTaskList(wfTask);
        return getDataTable(list);
    }

    /**
     * 查询任务数量
     */
    @Operation(summary = "查询任务数量")
    @PostMapping("/count")
    public AjaxResult count() {
        return AjaxResult.success(wfTaskService.selectWfTaskCountByUserId(String.valueOf(getLoginUser().getUserId())));
    }

    /**
     * 查询流程待办任务列表
     */
    @Operation(summary = "查询流程待办任务列表")
    @GetMapping("/todo")
    public TableDataInfo todo(@RequestParam(value = "tableName", required = false) String tableName) {
        WfTaskDTO wfTask = new WfTaskDTO();
        wfTask.setUserId(String.valueOf(getLoginUser().getUserId()));
        wfTask.setTaskDone("0");
        wfTask.setTableName(tableName);
        return list(wfTask);
    }

    /**
     * 查询流程已办任务列表
     */
    @Operation(summary = "查询流程已办任务列表")
    @GetMapping("/done")
    public TableDataInfo done(@RequestParam(value = "tableName", required = false) String tableName,
                              @RequestParam(value = "completed", required = false) Boolean completed) {
        WfTaskDTO wfTask = new WfTaskDTO();
        wfTask.setUserId(String.valueOf(getLoginUser().getUserId()));
        wfTask.setTaskDone("1");
        wfTask.setTableName(tableName);
        wfTask.setProcessDone(completed);
        return list(wfTask);
    }

    /**
     * 获取流程任务详细信息
     */
    @Operation(summary = "获取流程任务详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wfTaskService.selectCubeWfTaskById(id));
    }

    /**
     * 新增流程任务
     */
    @Operation(summary = "新增流程任务")
    @Log(title = "流程任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WfTask wfTask) {
        return toAjax(wfTaskService.insertCubeWfTask(wfTask));
    }

    /**
     * 修改流程任务
     */
    @Operation(summary = "修改流程任务")
    @Log(title = "流程任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WfTask wfTask) {
        return toAjax(wfTaskService.updateCubeWfTask(wfTask));
    }

    /**
     * 删除流程任务
     */
    @Operation(summary = "删除流程任务")
    @Log(title = "流程任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wfTaskService.deleteCubeWfTaskByIds(ids));
    }

    /**
     * 执行
     */
    @Operation(summary = "执行流程")
    @PostMapping("/execute")
    public AjaxResult execute(@RequestBody WfTaskDTO wfTaskDto) {
        ApproveStrategy approveStrategy = approveStrategyFactory.getApproveStrategy(wfTaskDto.getExecuteType());
        Assert.notNull(approveStrategy, "未定义的执行类型");
        return approveStrategy.approve(wfTaskDto);
    }

    /**
     * 获取下一节点信息
     */
    @Operation(summary = "获取提交信息")
    @PostMapping("/nextEdgeInfo")
    public AjaxResult nextEdgeInfo(@RequestBody WfGetNextEdgeDTO wfGetNextEdgeDto) {
        return AjaxResult.success(wfTaskService.nextEdgeInfo(wfGetNextEdgeDto.getTableId(), wfGetNextEdgeDto.getTaskId(), wfGetNextEdgeDto.getDataId(), wfGetNextEdgeDto.getFlowType()));
    }

    /**
     * 查询多维流程子任务列表
     */
    @Operation(summary = "查询多维流程子任务列表")
    @GetMapping("/childTaskList")
    public AjaxResult childTaskList(WfTaskDTO wfTaskDto) {
        return AjaxResult.success(wfTaskService.childTaskList(wfTaskDto));
    }

    @Operation(summary = "查看节点状态")
    @GetMapping("/viewNodeState")
    public AjaxResult viewNodeState(WfTaskDTO wfTaskDto) {
        return AjaxResult.success(wfTaskService.viewNodeState(wfTaskDto));
    }

}
