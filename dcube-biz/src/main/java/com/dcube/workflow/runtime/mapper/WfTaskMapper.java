package com.dcube.workflow.runtime.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.vo.WfTaskVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 流程任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface WfTaskMapper extends BaseMapper<WfTask> {
    /**
     * 查询流程任务
     *
     * @param id 流程任务主键
     * @return 流程任务
     */
    WfTask selectCubeWfTaskById(Long id);

    /**
     * 查询流程任务列表
     *
     * @param wfTask 流程任务
     * @return 流程任务集合
     */
    List<WfTaskVO> selectCubeWfTaskList(WfTaskDTO wfTask);

    /**
     * 新增流程任务
     *
     * @param wfTask 流程任务
     * @return 结果
     */
    int insertCubeWfTask(WfTask wfTask);

    /**
     * 修改流程任务
     *
     * @param wfTask 流程任务
     * @return 结果
     */
    int updateCubeWfTask(WfTask wfTask);

    /**
     * 删除流程任务
     *
     * @param id 流程任务主键
     * @return 结果
     */
    int deleteCubeWfTaskById(Long id);

    /**
     * 批量删除流程任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCubeWfTaskByIds(Long[] ids);

    int selectCubeWfTaskCount(WfTaskDTO wfTask);

    @MapKey(("task_done"))
    List<Map<String, Object>> selectWfTaskCountByUserId(@Param("userId") String userId);
}
