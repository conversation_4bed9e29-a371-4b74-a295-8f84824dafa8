package com.dcube.tran.element;

import com.dcube.cube.core.FactTable;
import com.dcube.tran.util.ClassSize;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 多维记录
 */
@ToString
public class CubeRecord implements Record {

    private static final int RECORD_AVERGAE_COLUMN_NUMBER = 16;

    //    private String ruleExpress;
    private FactTable.Record record;

    private int byteSize;

    // 首先是Record本身需要的内存
    private int memorySize = ClassSize.DefaultRecordHead;

    public CubeRecord() {
    }

    public CubeRecord(FactTable.Record record) {
        this.record = record;
    }

    public FactTable.Record getRecord() {
        return record;
    }

    public void setRecord(FactTable.Record record) {
        this.record = record;
    }

    @Override
    public void addColumn(Column column) {
        incrByteSize(column);
    }

    @Override
    public void addColumns(List<Column> columns) {
        incrByteSize(columns);
    }

    @Override
    public Column getColumn(int i) {
        return null;
    }

    @Override
    public void setColumn(int i, final Column column) {
        incrByteSize(getColumn(i));
    }

    @Override
    public int getColumnNumber() {
        return 0;
    }

    @Override
    public int getByteSize() {
        return byteSize;
    }

    @Override
    public int getMemorySize() {
        return memorySize;
    }

    @Override
    public List<Column> getColumnList() {
        return Collections.emptyList();
    }

    private void decrByteSize(final Column column) {
        if (null == column) {
            return;
        }

        byteSize -= column.getByteSize();

        //内存的占用是column对象的头 再加实际大小
        memorySize = memorySize - ClassSize.ColumnHead - column.getByteSize();
    }

    private void incrByteSize(final Column column) {
        if (null == column) {
            return;
        }

        byteSize += column.getByteSize();

        //内存的占用是column对象的头 再加实际大小
        memorySize = memorySize + ClassSize.ColumnHead + column.getByteSize();
    }

    private void incrByteSize(final List<Column> columns) {
        if (CollectionUtils.isEmpty(columns)) {
            return;
        }

        int sum = columns.stream().mapToInt(Column::getByteSize).sum();

        byteSize += sum;

        //内存的占用是column对象的头 再加实际大小
        memorySize = memorySize + (ClassSize.ColumnHead * columns.size()) + sum;
    }

}