package com.dcube.workflow.define.dto;

import com.dcube.workflow.define.domain.WfDimDefineDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


@Data
@EqualsAndHashCode
@ToString
public class WfDimDefineDetailSaveBatchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "流程定义id")
    private Long defineId;

    private List<WfDimDefineDetail> dimDefineDetails;


}
