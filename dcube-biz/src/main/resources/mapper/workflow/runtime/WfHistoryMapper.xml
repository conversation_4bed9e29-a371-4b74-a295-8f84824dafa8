<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.workflow.runtime.mapper.WfHistoryMapper">

    <resultMap type="WfHistory" id="CubeWfHistoryResult">
        <result property="id" column="id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="defineId" column="define_id"/>
        <result property="taskId" column="task_id"/>
        <result property="tableId" column="table_id"/>
        <result property="dataId" column="data_id"/>
        <result property="commentContent" column="comment_content"/>
        <result property="nodeId" column="node_id"/>
        <result property="nodeName" column="node_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="operateType" column="operate_type"/>
        <result property="centralizeDimDefineId" column="centralize_dim_define_id"/>
    </resultMap>

    <sql id="selectCubeWfHistoryVo">
        select id,
               instance_id,
               define_id,
               task_id,
               table_id,
               data_id,
               comment_content,
               node_id,
               node_name,
               user_id,
               user_name,
               create_by,
               create_time,
               update_by,
               update_time,
               operate_type
        from cube_wf_history
    </sql>

    <select id="selectCubeWfHistoryList" parameterType="WfHistory" resultMap="CubeWfHistoryResult">
        <include refid="selectCubeWfHistoryVo"/>
        <where>
            del_flag = '0'
            <if test="instanceId != null">
                and instance_id = #{instanceId}
            </if>
            <if test="defineId != null">
                and define_id = #{defineId}
            </if>
            <if test="tableId != null">
                and table_id = #{tableId}
            </if>
            <if test="dataId != null  and dataId != ''">
                and data_id = #{dataId}
            </if>
            <if test="commentContent != null  and commentContent != ''">
                and comment_content = #{commentContent}
            </if>
            <if test="nodeId != null and nodeId != ''">
                and node_id = #{nodeId}
            </if>
            <if test="nodeName != null  and nodeName != ''">
                and node_name like concat('%', #{nodeName}, '%')
            </if>
            <if test="userId != null  and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userName != null  and userName != ''">
                and user_name like concat('%', #{userName}, '%')
            </if>
            <choose>
                <when test="centralizeDimDefineId != null and centralizeDimDefineId != ''">
                    and centralize_dim_define_id = #{centralizeDimDefineId}
                </when>
                <otherwise>
                    and centralize_dim_define_id is null
                </otherwise>
            </choose>
        </where>
        order by create_time
        <choose>
            <when test="isAsc != null and (isAsc == 'desc' or isAsc == 'asc')">
                ${isAsc}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="selectCubeWfHistoryById" parameterType="Long" resultMap="CubeWfHistoryResult">
        <include refid="selectCubeWfHistoryVo"/>
        where id = #{id} and del_flag='0'
    </select>
    <select id="getByInstanceId" resultType="com.dcube.workflow.runtime.vo.WfHistoryVO">
        select h.id,
               h.instance_id,
               h.define_id,
               h.task_id,
               h.table_id,
               h.data_id,
               h.comment_content,
               h.node_id,
               h.node_name,
               h.user_id,
               h.user_name,
               h.create_by,
               h.create_time,
               h.update_by,
               h.update_time,
               t.approve_role_id,
               t.approve_role_name,
               t.next_edge_id,
               t.next_edge_name,
               t.next_node_id,
               t.next_node_name,
               t.cost
        from cube_wf_history h
                 left join cube_wf_task t on h.task_id = t.id
        where h.instance_id = #{instanceId}
          and h.del_flag = '0'
        order by h.create_time desc
    </select>

    <insert id="insertCubeWfHistory" parameterType="WfHistory" useGeneratedKeys="true" keyProperty="id">
        insert into cube_wf_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="instanceId != null">instance_id,</if>
            <if test="defineId != null">define_id,</if>
            <if test="tableId != null">table_id,</if>
            <if test="dataId != null">data_id,</if>
            <if test="commentContent != null">comment_content,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="nodeName != null">node_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="instanceId != null">#{instanceId},</if>
            <if test="defineId != null">#{defineId},</if>
            <if test="tableId != null">#{tableId},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="commentContent != null">#{commentContent},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId},</if>
            <if test="nodeName != null">#{nodeName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCubeWfHistory" parameterType="WfHistory">
        update cube_wf_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="defineId != null">define_id = #{defineId},</if>
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="commentContent != null">comment_content = #{commentContent},</if>
            <if test="nodeId != null and nodeId != ''">node_id = #{nodeId},</if>
            <if test="nodeName != null">node_name = #{nodeName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCubeWfHistoryById" parameterType="Long">
        update cube_wf_history
        set del_flag='0'
        where id = #{id}
    </delete>

    <delete id="deleteCubeWfHistoryByIds" parameterType="String">
        update cube_wf_history
        set del_flag='0'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>