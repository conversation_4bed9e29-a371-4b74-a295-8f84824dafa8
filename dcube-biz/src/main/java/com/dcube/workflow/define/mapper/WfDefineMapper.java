package com.dcube.workflow.define.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcube.workflow.define.domain.WfDefine;

import java.util.List;

/**
 * 流程定义Mapper接口
 *
 * @date 2024-02-25
 */
public interface WfDefineMapper extends BaseMapper<WfDefine> {
    /**
     * 查询流程定义
     *
     * @param id 流程定义主键
     * @return 流程定义
     */
    WfDefine selectCubeWfDefineById(Long id);

    /**
     * 查询流程定义列表
     *
     * @param wfDefine 流程定义
     * @return 流程定义集合
     */
    List<WfDefine> selectCubeWfDefineList(WfDefine wfDefine);

    /**
     * 新增流程定义
     *
     * @param wfDefine 流程定义
     * @return 结果
     */
    int insertCubeWfDefine(WfDefine wfDefine);

    /**
     * 修改流程定义
     *
     * @param wfDefine 流程定义
     * @return 结果
     */
    int updateCubeWfDefine(WfDefine wfDefine);

    /**
     * 删除流程定义
     *
     * @param id 流程定义主键
     * @return 结果
     */
    int deleteCubeWfDefineById(Long id);

    /**
     * 批量删除流程定义
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCubeWfDefineByIds(Long[] ids);

}
