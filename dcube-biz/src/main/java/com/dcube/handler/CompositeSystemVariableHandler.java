package com.dcube.handler;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.dcube.common.enums.SystemVariable;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.system.service.ISysDeptService;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @创建人 zhouhx
 * @创建时间 2024/6/12 11:55
 * @描述
 */
public class CompositeSystemVariableHandler implements SystemVariableHandler {

    @Override
    public Object handle(SystemVariable variable) {
        switch (variable) {
            case CURRENT_TIME:
                return LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
            case CURRENT_DATE:
                return LocalDateTimeUtil.format(LocalDate.now(), "yyyy-MM-dd");
            case CURRENT_USER:
                return SecurityUtils.getUsername();
            case CURRENT_USER_NAME:
                return SecurityUtils.getLoginUser().getUser().getNickName();
            case CURRENT_DEPARTMENT:
                return SpringUtils.getBean(ISysDeptService.class).selectDeptById(SecurityUtils.getDeptId()).getDeptName();
            default:
                throw new IllegalArgumentException("未知的系统变量: " + variable);
        }
    }
}
