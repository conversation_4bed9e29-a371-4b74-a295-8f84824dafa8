package com.dcube.workflow.runtime.strategy.factory;

import com.dcube.workflow.runtime.strategy.ApproveStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ApproveStrategyFactory {

    @Autowired
    private Map<String, ApproveStrategy> services;

    public ApproveStrategy getApproveStrategy(String code) {
        return services.get(code);
    }

}
