package com.dcube.workflow.runtime.vo;

import com.dcube.biz.base.BaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程历史任务对象 cube_wf_history
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class WfHistoryVO extends BaseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long instanceId;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defineId;

    /**
     * 表id
     */
    @Schema(description = "表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 任务id
     */
    @Schema(description = "任务id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 流程状态列编码
     */
    @Schema(description = "流程状态列编码")
    private String dataId;

    /**
     * 审批意见内容
     */
    @Schema(description = "审批意见内容")
    private String commentContent;

    /**
     * 所属节点id
     */
    @Schema(description = "所属节点id")
    private String nodeId;

    /**
     * 所属节点名称
     */
    @Schema(description = "所属节点名称")
    private String nodeName;

    /**
     * 审批人id
     */
    @Schema(description = "审批人id")
    private String userId;

    /**
     * 审批人名称
     */
    @Schema(description = "审批人名称")
    private String userName;

    /**
     * 耗时
     */
    @Schema(description = "耗时（ms）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long cost;

    /**
     * 停留时间
     */
    @Schema(description = "停留时间")
    private String stayTime;

    /**
     * 审批角色id
     */
    @Schema(description = "审批角色id")
    private String approveRoleId;
    /**
     * 审批角色名称
     */
    @Schema(description = "审批角色名称")
    private String approveRoleName;
    /**
     * 提交路径id
     */
    @Schema(description = "提交路径id")
    private String nextEdgeId;
    /**
     * 提交路径名称
     */
    @Schema(description = "提交路径名称")
    private String nextEdgeName;
    /**
     * 下一节点id
     */
    @Schema(description = "下一节点id")
    private String nextNodeId;
    /**
     * 下一节点名称
     */
    @Schema(description = "下一节点名称")
    private String nextNodeName;
    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
