package com.dcube.rule.grid.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.*;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.IDimTableService;
import com.dcube.biz.service.ITableRelService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.*;
import com.dcube.common.annotation.ThreadLocalCache;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.constant.enums.StateEnum;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.enums.ThreadLocalCacheType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.DateUtils;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.grid.TableMetaData;
import com.dcube.quartz.domain.CubeJobDetail;
import com.dcube.quartz.service.ICubeJobDetailService;
import com.dcube.rule.grid.RuleGraph;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.constants.enums.RuleOperatorEnum;
import com.dcube.rule.grid.domain.Rule;
import com.dcube.rule.grid.domain.RuleFunc;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.function.ProductFunction;
import com.dcube.rule.grid.mapper.RuleMapper;
import com.dcube.rule.grid.service.IRuleFuncService;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.util.ExpressUtils;
import com.dcube.rule.grid.vo.JobRuleVO;
import com.dcube.rule.grid.vo.RuleRefTraceViewVO;
import com.dcube.rule.grid.vo.RuleVO;
import com.dcube.rule.grid.vo.ValidateRuleVO;
import com.dcube.system.service.ISysConfigService;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service
@Slf4j
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements IRuleService {
    @Autowired
    private ITableService tableService;
    @Autowired
    private ITableRelService tableRelService;
    @Autowired
    private RuleMapper ruleMapper;
    @Autowired
    private ExpressUtils expressUtils;
    @Autowired
    @Qualifier("cubeRuleCache")
    private Cache<String, Object> cubeRuleCache;
    @Autowired
    @Qualifier("ruleGraphCache")
    private Cache<String, RuleGraph> ruleGraphCache;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    @Qualifier("taskExecutorCallerRun")
    private ThreadPoolTaskExecutor taskExecutorCallerRun;
    @Autowired
    private IRuleFuncService ruleFuncService;
    @Autowired
    private ICubeJobDetailService jobDetailService;
    @Autowired
    private GlobalExceptionProperties globalExceptionProperties;

    private static final Pattern expressOPattern = Pattern.compile("o\\(\"(.*?)\"\\)");
    private static final Pattern expressPPattern = Pattern.compile("p\\(\"(.*?)\"\\)");
    private static final Pattern expressCPattern = Pattern.compile("c\\(\"(.*?)\",\"(.*?)\"\\)");
    private static final String expressTPatternString = "t\\(\"(.*?)\",\"(.*?)\"\\)";
    private static final Pattern expressTPattern = Pattern.compile(expressTPatternString);
//    public static final Pattern expressStrToDatePattern = Pattern.compile("strToDate\\((.*?),\"(.*?)\"\\)");
//    public static final Pattern expressDateToStrPattern = Pattern.compile("dateToStr\\((.*?),\"(.*?)\"\\)");

    public static final List<String> ARITHMETIC_OPERATOR = Arrays.asList("+", "-", "*", "/", "%");
    private static final List<String> FUNC_OPERATOR = Arrays.asList("count", "vmax", "vmin", "vavgw");

    @Autowired
    private IDimTableService dimTableService;

    /**
     * 执行规则计算
     *
     * @param tableId
     * @param columnCodeList
     */
    @Override
    public void doExecuteRuleExpress(Integer tableId, List<String> columnCodeList, ReportDto<AtomicLong> reportDto) {
        TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
        String memTableName = tableVo.getMemTableName();
        Object[][] tableData = MemGridUtils.getTableData(memTableName);
        doExecuteRuleExpress(tableId, columnCodeList, tableData, memTableName, reportDto);
    }

    /**
     * 执行规则计算
     *
     * @param tableId
     * @param columnCodeList
     * @param tableData
     */
    @Override
    public void doExecuteRuleExpress(Integer tableId, List<String> columnCodeList, Object[][] tableData, String memTableName, ReportDto<AtomicLong> reportDto) {
        log.info("==================> start 执行表{}的规则，列：{}", tableId, columnCodeList);
        Map<Integer, Long> indexRuleIdMap = null;
        try {
            if (tableData == null || tableData.length == 0) {
                return;
            }
            initLocalCache(tableId, columnCodeList);
            Map<Integer, String> indexColumnMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP);
            Map<Integer, String> indexExpressMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_EXPRESS_MAP);
            indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
            if (MapUtils.isNotEmpty(indexExpressMap)) {
                reportDto.setTaskName(reportDto.getTaskName() + "==>正在初始化计算规则缓存");
                for (Map.Entry<Integer, String> entry : indexExpressMap.entrySet()) {
                    initExpressCache(tableId, MapUtils.getString(indexColumnMap, entry.getKey()), entry.getValue());
                }
                reportDto.setTaskName(StringUtils.removeEnd(reportDto.getTaskName(), "==>正在初始化计算规则缓存"));

                int threadSize = sysConfigService.getRuleExecuteThreadSize();
                int length = tableData.length;
                reportDto.setTotalRecord((long) length);
                reportDto.setCurrentRecord(new AtomicLong(0));
                TaskReport.put(reportDto);
                // 调整threadSize，如果length小于threadSize，只使用length个线程
                threadSize = Math.min(length, threadSize);
                log.info("实际的执行规则线程数：{}", threadSize);
                CompletableFuture<Void> future = new CompletableFuture<>();
                // 异步，分批
                // 计算每个线程的基本任务数
                int batchSize = length / threadSize;
                int remainder = length % threadSize;
                TableMetaData tableMetaData = MemGridUtils.getTableMetaData(memTableName);
                List<CompletableFuture<Void>> completableFutures = new ArrayList<>(threadSize);
                // 分配任务到线程
                for (int i = 0; i < threadSize; i++) {
                    // 起始索引
                    int startIndex = i * batchSize + Math.min(i, remainder);
                    // 结束索引，确保不越界
                    int endIndex = (i + 1) * batchSize + Math.min(i + 1, remainder);

                    // 确保最后一个线程不会越界
                    endIndex = Math.min(endIndex, length);
                    final int _endIndex = endIndex;
                    final ReportDto _reportDto = reportDto;
                    final Map<Integer, Long> _indexRuleIdMap = indexRuleIdMap;
                    CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(TtlRunnable.get(() -> {
                        try {
                            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CURRENT_TABLE_ID, tableId);
                            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP, indexColumnMap);
                            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_EXPRESS_MAP, indexExpressMap);
                            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP, _indexRuleIdMap);
                            Map<String, Integer> reverseIndexColumnMap = indexColumnMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2));
                            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP, reverseIndexColumnMap);
                            for (int j = startIndex; j < _endIndex; j++) {
                                Object[] data = tableData[j];
                                for (Map.Entry<Integer, String> entry : indexExpressMap.entrySet()) {
                                    Integer index = entry.getKey();
                                    Object value = getComputeValue(MapUtils.getLong(_indexRuleIdMap, index), entry.getValue(), data, MapUtils.getString(indexColumnMap, index));
                                    data[index] = MemGridUtils.getValue(tableMetaData.getColumnTypes().get(index), value);
                                }
//                                reportDto.getTaskCount().incrementAndGet();
                                ReportTool.appendRecord(_reportDto, 1);
                            }
                        } finally {
                            ThreadLocalUtils.clear();
                        }
                    }), taskExecutorCallerRun).exceptionally(e -> {
                        future.completeExceptionally(e);
                        return null;
                    });
                    completableFutures.add(completableFuture);
                }
                future.exceptionally(e -> {
                    completableFutures.forEach(completableFuture -> {
                        completableFuture.completeExceptionally(e);
                    });
                    return null;
                });
                CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).exceptionally(e -> {
                    log.error("执行计算规则时出现异常", e);
                    reportDto.setState(StateEnum.FAILED.getCode()).setStatus(TaskStatusEnums.ERROR);
                    String message;
                    if (e instanceof CompletionException) {
                        message = ExceptionUtil.getLocalizedMessage(e.getCause());
                    } else {
                        message = ExceptionUtil.getLocalizedMessage(e);
                    }
                    reportDto.setMsg(message);
                    throw new RuleExecutionException(message);
                }).join();
            }
        } finally {
            log.info("==================> end 执行表{}的规则，列：{}", tableId, columnCodeList);
            if (reportDto != null) {
                TaskReport.finished(reportDto);
            }
            removeFuncCache(indexRuleIdMap);
        }
    }

    private void initExpressCache(Integer tableId, String columnCode, String express) {
        long start = System.currentTimeMillis();
        Matcher tMatcher = expressTPattern.matcher(express);
        while (tMatcher.find()) {
            String name = tMatcher.group(1);
            initTableLocalCache(getTableByName(name).getId());
        }
        handleAllocaFunc(express, tableId, columnCode);
        handleVsumFunc(express, tableId, columnCode);
        handleVcountFunc(express, tableId, columnCode);
        handleVmaxFunc(express, tableId, columnCode);
        handleVminFunc(express, tableId, columnCode);
        handleVavgFunc(express, tableId, columnCode);
        handleVavgwFunc(express, tableId, columnCode);

        // 子表函数
        handleCsumFunc(express, tableId, columnCode);
        handleCavgFunc(express, tableId, columnCode);
        handleCcountFunc(express, tableId, columnCode);
        handleCmaxFunc(express, tableId, columnCode);
        handleCminFunc(express, tableId, columnCode);

        // vlookup
        handleVlookupFunc(express, tableId, columnCode);
        handleVlookupsFunc(express, tableId, columnCode);

        // 累乘
        handleProductFunc(express, tableId, columnCode);

        // 初始化数据缓存
        initPDataCache(express, tableId, columnCode);
        initCDataCache(express, tableId, columnCode);
        long cost = System.currentTimeMillis() - start;
        log.info("初始化表达式缓存耗时：{}ms，tableId：{},columnCode：{}, express：{}", cost, tableId, columnCode, express);
        if (cost > 10000) {
            log.warn("⚠️初始化表达式缓存耗时：{}ms，tableId：{},columnCode：{}, express：{}", cost, tableId, columnCode, express);
        }
    }

    /**
     *  父表数据缓存
     */
    private void initPDataCache(String express, Integer tableId, String columnCode) {
        if (StringUtils.isEmpty(express)) {
            return;
        }
        Integer parentTableId = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.P_TABLE_ID + tableId);
        if (parentTableId == null) {
            return;
        }
        initTableLocalCache(parentTableId);
        TableVo pTable = SpringUtils.getAopProxy(this).getTableVoById(parentTableId);
        if (pTable == null) {
            throw new RuleExecutionException("未找到表" + parentTableId + "！");
        }
        TableMetaData pTableMetaData = MemGridUtils.getTableMetaData(pTable.getMemTableName());
        if (pTableMetaData == null) {
            throw new RuleExecutionException("未找到表" + pTable.getTableName() + "的metadata！");
        }
        Matcher pMatcher = expressPPattern.matcher(express);
        while (pMatcher.find()) {
            String name = pMatcher.group(1);
            Integer tableLevel = pTable.getTableLevel();
            String pColumnName = String.valueOf(name);
            String refColumnCode = ExpressUtils.getRefColumnCode(parentTableId, pColumnName);
            if (StringUtils.isEmpty(refColumnCode)) {
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
                Map<String, String> tableCodeNameMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, TableMetaJson::getName));
                throw new RuleExecutionException("执行表【" + tableVo.getTableName() + "】列【" + MapUtils.getString(tableCodeNameMap, columnCode) + "】的规则时，父表不存在列：" + pColumnName);
            }
            Map<String, Object> cacheMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleOperatorEnum.P.name() + "_" + pTable.getId() + "_" + refColumnCode);
            if (cacheMap == null) {
                cacheMap = new HashMap<>();
                List<String> columnCodes = new ArrayList<>();
                for (int i = 0; i <= tableLevel; i++) {
                    columnCodes.add(pTableMetaData.getColumnNames().get(i));
                }
                Object[][] tableData = SpringUtils.getAopProxy(this).getTableData(pTable.getMemTableName());
                if (ArrayUtils.isNotEmpty(tableData)) {
                    Map<String, Integer> columnIndexMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + pTable.getId());
                    for (Object[] row : tableData) {
                        StringBuilder stringBuilder = new StringBuilder();
                        for (int i = 0, size = columnCodes.size(); i < size; i++) {
                            stringBuilder.append(row[i]);
                            if (i != size - 1) {
                                stringBuilder.append("_");
                            }
                        }
                        cacheMap.put(stringBuilder.toString(), row[MapUtils.getObject(columnIndexMap, refColumnCode)]);
                    }
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleOperatorEnum.P.name() + "_" + pTable.getId() + "_" + refColumnCode, cacheMap);
            }
        }
    }

    /**
     *  子表数据缓存
     */
    private void initCDataCache(String express, Integer tableId, String columnCode) {
        List<TableRel> childAssociateTables = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.CHILD_ASSOCIATE_TABLES + tableId);
        if (CollectionUtils.isEmpty(childAssociateTables)) {
            return;
        }
        Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
        Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
        Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
        if (!Boolean.FALSE.equals(ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLLECT_C_REF + ruleId))) {
            Matcher cMatcher = expressCPattern.matcher(express);
            while (cMatcher.find()) {
                String tableName = cMatcher.group(1);
                String columnName = cMatcher.group(2);
                List<Integer> childTableIds = childAssociateTables.stream().map(TableRel::getChildTableId).collect(Collectors.toList());
                // 当前表关联的子表
                Table cTable = SpringUtils.getAopProxy(this).getTableByIdsAndName(childTableIds, tableName);
                if (Objects.isNull(cTable)) {
                    throw new RuleExecutionException("未找到子表【" + tableName + "】");
                }
                SpringUtils.getAopProxy(this).initTableLocalCache(cTable.getId());
                String refColumnCode = ExpressUtils.getRefColumnCode(cTable.getId(), columnName);
                Map resMap;
                // 表数据缓存
                Object ruleCache = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.TABLE_DATA + cTable.getMemTableName() + "_" + refColumnCode);
                if (ruleCache != null) {
                    continue;
                }
                Object[][] tableData = SpringUtils.getAopProxy(this).getTableData(cTable);
                if (tableData == null || tableData.length == 0) {
                    resMap = Collections.emptyMap();
                } else {
                    resMap = Maps.newHashMap();
                    Map<String, Integer> cColumnIndexMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + cTable.getId());
                    for (Object[] cRowData : tableData) {
                        StringBuilder stringBuilder = new StringBuilder();
                        for (int i = 0; i < cTable.getTableLevel(); i++) {
                            stringBuilder.append(cRowData[cTable.getTableLevel() - 1]);
                            if (i != cTable.getTableLevel() - 1) {
                                stringBuilder.append("_");
                            }
                        }
                        Map map = (Map) resMap.computeIfAbsent(stringBuilder.toString(), k -> Maps.newHashMap());
                        map.put(String.valueOf(cRowData[cTable.getTableLevel()]), cRowData[MapUtils.getObject(cColumnIndexMap, refColumnCode)]);
                    }
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_DATA + cTable.getMemTableName() + "_" + refColumnCode, resMap);
            }
        }
    }

    /**
     * 累乘
     */
    private void handleProductFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "product(")) {
            String product = ExpressUtils.extractFuncContent(express, "product(");
            if (StringUtils.isNotEmpty(product)) {
                Matcher productOMatcher = expressOPattern.matcher(product);
                String productColumnName = "";
                if (productOMatcher.find()) {
                    productColumnName = productOMatcher.group(1);
                }

                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tableId);
                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
                String productColumnCode = MapUtils.getString(columnNameCodeMap, productColumnName);
                if (StringUtils.isEmpty(productColumnCode)) {
                    throw new RuleExecutionException("product函数中：" + productColumnName + "未找到！");
                }

                String avgColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(productColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    avgColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    avgColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    avgColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(avgColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("product函数中：【" + productColumnName + "】非数字类型不参与计算！");
                }
                Object[][] tableData = SpringUtils.getAopProxy(this).getTableData(tableVo.getMemTableName());
                calculateProduct(tableData, ruleId, MapUtils.getInteger(columnIndexMap, productColumnCode), tableVo.getTableLevel());
            } else {
                throw new RuleExecutionException("product函数定义出现错误，请检查！");
            }
        }
    }

    /**
     * 检查边界
     */
    private static BigDecimal applyBounds(Object obj) {
        BigDecimal value = BigDecimalUtils.convertDigDecimal(obj);
        if (value.compareTo(ProductFunction.UPPER_BOUND) > 0) {
            return ProductFunction.UPPER_BOUND;
        } else if (value.compareTo(ProductFunction.LOWER_BOUND) < 0) {
            return ProductFunction.LOWER_BOUND;
        } else {
            return value;
        }
    }

    public static void calculateProduct(Object[][] tableData, Long ruleId, Integer productColumnCodeIndex, Integer tableLevel) {
        if (ArrayUtils.isEmpty(tableData)) {
            return;
        }
        // 主表
        if (tableLevel == 0) {
            // 创建原数组的副本
            Object[][] tableDataCopy = Arrays.copyOf(tableData, tableData.length);
            for (int i = 0; i < tableData.length; i++) {
                tableDataCopy[i] = Arrays.copyOf(tableData[i], tableData[i].length);
            }
            // 对副本进行排序
            Arrays.sort(tableDataCopy, (o1, o2) -> {
                Integer value1 = (Integer) o1[0];
                Integer value2 = (Integer) o2[0];
                return value1.compareTo(value2);
            });

            int len = tableDataCopy.length;
            List<BigDecimal> cacheList = new ArrayList<>(len);
            for (int i = 0; i < len; i++) {
                Object[] objects = tableDataCopy[i];
                if (ArrayUtils.isEmpty(objects)) {
                    cacheList.add(BigDecimal.ZERO);
                    continue;
                }
                if (i == 0) {
                    // 首行的特殊处理
                    Object object = objects[productColumnCodeIndex];
                    BigDecimal bigDecimal;
                    if (object == null) {
                        bigDecimal = BigDecimal.ZERO;
                    } else {
                        bigDecimal = applyBounds(objects[productColumnCodeIndex]);
                    }
                    cacheList.add(bigDecimal);
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.PRODUCT_CACHE + ruleId + "_" + objects[0], bigDecimal);
                } else {
                    // 基于上一行的结果进行累乘
                    Object object = objects[productColumnCodeIndex];
                    BigDecimal bigDecimal;
                    if (object == null) {
                        bigDecimal = BigDecimal.ZERO;
                    } else {
                        bigDecimal = applyBounds(cacheList.get(i - 1).multiply(BigDecimalUtils.convertDigDecimal(object)));
                    }
                    cacheList.add(bigDecimal);
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.PRODUCT_CACHE + ruleId + "_" + objects[0], bigDecimal);
                }
            }
        } else {
            // 子表，要先分组
            Map<String, List<Object[]>> groups = new HashMap<>();
            for (Object[] row : tableData) {
                StringBuilder key = new StringBuilder();
                for (int i = 0; i < tableLevel; i++) {
                    key.append(row[i]).append("_");
                }
                groups.computeIfAbsent(key.toString(), k -> new ArrayList<>()).add(row);
            }

            // 排序
            for (Map.Entry<String, List<Object[]>> entry : groups.entrySet()) {
                entry.getValue().sort(Comparator.comparing(row -> (Comparable) row[tableLevel]));
            }

            // 计算
            for (Map.Entry<String, List<Object[]>> entry : groups.entrySet()) {
                List<Object[]> value = entry.getValue();
                int len = value.size();
                List<BigDecimal> cacheList = new ArrayList<>(len);
                for (int i = 0; i < len; i++) {
                    Object[] objects = value.get(i);
                    if (ArrayUtils.isEmpty(objects)) {
                        cacheList.add(BigDecimal.ZERO);
                        continue;
                    }
                    StringBuilder key = new StringBuilder();
                    for (int j = 0; j <= tableLevel; j++) {
                        key.append(objects[j]);
                        if (j != tableLevel) {
                            key.append("_");
                        }
                    }
                    if (i == 0) {
                        // 首行的特殊处理
                        Object object = objects[productColumnCodeIndex];
                        BigDecimal bigDecimal;
                        if (object == null) {
                            bigDecimal = BigDecimal.ZERO;
                        } else {
                            bigDecimal = applyBounds(objects[productColumnCodeIndex]);
                        }
                        cacheList.add(bigDecimal);
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.PRODUCT_CACHE + ruleId + "_" + key, bigDecimal);
                    } else {
                        // 基于上一行的结果进行累乘
                        Object object = objects[productColumnCodeIndex];
                        BigDecimal bigDecimal;
                        if (object == null) {
                            bigDecimal = BigDecimal.ZERO;
                        } else {
                            bigDecimal = applyBounds(cacheList.get(i - 1).multiply(BigDecimalUtils.convertDigDecimal(object)));
                        }
                        cacheList.add(bigDecimal);
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.PRODUCT_CACHE + ruleId + "_" + key, bigDecimal);
                    }
                }
            }
        }
    }

    private void handleCminFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "cmin(")) {
            String cmin = ExpressUtils.extractFuncContent(express, "cmin(");
            if (StringUtils.isNotEmpty(cmin)) {
                Matcher cminCMatcher = expressCPattern.matcher(cmin);
                String cName = "";
                String sumColumnName = "";
                if (cminCMatcher.find()) {
                    cName = cminCMatcher.group(1);
                    sumColumnName = cminCMatcher.group(2);
                }

                Integer cTableId = getTableByName(cName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + cTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(cTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("cmin函数中：" + sumColumnName + "未找到！");
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("cmin函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                Integer tableLevel = tableVo.getTableLevel();
                List<String> codes = new ArrayList<>(tableLevel);
                for (int i = 0; i < tableLevel; i++) {
                    codes.add(tableVo.getTableMetaJson().get(i).getCode());
                }
                String codeString = String.join(",", codes);
                String query = String.format("select %s, min(%s) as m from %s group by %s", codeString, sumColumnCode, tableName, codeString);
                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> cminCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    List<String> keyList = new ArrayList<>(tableLevel);
                    for (int i = 0; i < tableLevel; i++) {
                        keyList.add(MapUtils.getString(map, codes.get(i)));
                    }
                    cminCache.put(String.join("_", keyList), MapUtils.getObject(map, "m"));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CMIN + ruleId + "_" + cName, cminCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_C_REF + ruleId, Boolean.FALSE);

            } else {
                throw new RuleExecutionException("cmin函数定义出现错误，请检查！");
            }
        }
    }

    private void handleCmaxFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "cmax(")) {
            String cmax = ExpressUtils.extractFuncContent(express, "cmax(");
            if (StringUtils.isNotEmpty(cmax)) {
                Matcher cmaxCMatcher = expressCPattern.matcher(cmax);
                String cName = "";
                String sumColumnName = "";
                if (cmaxCMatcher.find()) {
                    cName = cmaxCMatcher.group(1);
                    sumColumnName = cmaxCMatcher.group(2);
                }

                Integer cTableId = getTableByName(cName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + cTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(cTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("cmax函数中：" + sumColumnName + "未找到！");
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("cmax函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                Integer tableLevel = tableVo.getTableLevel();
                List<String> codes = new ArrayList<>(tableLevel);
                for (int i = 0; i < tableLevel; i++) {
                    codes.add(tableVo.getTableMetaJson().get(i).getCode());
                }
                String codeString = String.join(",", codes);
                String query = String.format("select %s, max(%s) as m from %s group by %s", codeString, sumColumnCode, tableName, codeString);
                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> cmaxCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    List<String> keyList = new ArrayList<>(tableLevel);
                    for (int i = 0; i < tableLevel; i++) {
                        keyList.add(MapUtils.getString(map, codes.get(i)));
                    }
                    cmaxCache.put(String.join("_", keyList), MapUtils.getObject(map, "m"));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CMAX + ruleId + "_" + cName, cmaxCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_C_REF + ruleId, Boolean.FALSE);

            } else {
                throw new RuleExecutionException("cmax函数定义出现错误，请检查！");
            }
        }
    }

    private void handleCcountFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "ccount(")) {
            String ccount = ExpressUtils.extractFuncContent(express, "ccount(");
            if (StringUtils.isNotEmpty(ccount)) {
                Matcher ccountCMatcher = expressCPattern.matcher(ccount);
                String cName = "";
                String sumColumnName = "";
                if (ccountCMatcher.find()) {
                    cName = ccountCMatcher.group(1);
                    sumColumnName = ccountCMatcher.group(2);
                }

                Integer cTableId = getTableByName(cName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + cTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(cTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("ccount函数中：" + sumColumnName + "未找到！");
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("ccount函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                Integer tableLevel = tableVo.getTableLevel();
                List<String> codes = new ArrayList<>(tableLevel);
                for (int i = 0; i < tableLevel; i++) {
                    codes.add(tableVo.getTableMetaJson().get(i).getCode());
                }
                String codeString = String.join(",", codes);
                String query = String.format("select %s, count(%s) as c from %s group by %s", codeString, sumColumnCode, tableName, codeString);
                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> ccountCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    List<String> keyList = new ArrayList<>(tableLevel);
                    for (int i = 0; i < tableLevel; i++) {
                        keyList.add(MapUtils.getString(map, codes.get(i)));
                    }
                    ccountCache.put(String.join("_", keyList), MapUtils.getObject(map, "c"));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CCOUNT + ruleId + "_" + cName, ccountCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_C_REF + ruleId, Boolean.FALSE);

            } else {
                throw new RuleExecutionException("ccount函数定义出现错误，请检查！");
            }
        }
    }

    private void handleCavgFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "cavg(")) {
            String cavg = ExpressUtils.extractFuncContent(express, "cavg(");
            if (StringUtils.isNotEmpty(cavg)) {
                Matcher cavgCMatcher = expressCPattern.matcher(cavg);
                String cName = "";
                String sumColumnName = "";
                if (cavgCMatcher.find()) {
                    cName = cavgCMatcher.group(1);
                    sumColumnName = cavgCMatcher.group(2);
                }

                Integer cTableId = getTableByName(cName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + cTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(cTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("cavg函数中：" + sumColumnName + "未找到！");
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("cavg函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                Integer tableLevel = tableVo.getTableLevel();
                List<String> codes = new ArrayList<>(tableLevel);
                for (int i = 0; i < tableLevel; i++) {
                    codes.add(tableVo.getTableMetaJson().get(i).getCode());
                }
                String codeString = String.join(",", codes);
                String query = String.format("select %s, avg(%s) as a from %s group by %s", codeString, sumColumnCode, tableName, codeString);
                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> cavgCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    List<String> keyList = new ArrayList<>(tableLevel);
                    for (int i = 0; i < tableLevel; i++) {
                        keyList.add(MapUtils.getString(map, codes.get(i)));
                    }
                    cavgCache.put(String.join("_", keyList), MapUtils.getObject(map, "a"));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CAVG + ruleId + "_" + cName, cavgCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_C_REF + ruleId, Boolean.FALSE);

            } else {
                throw new RuleExecutionException("cavg函数定义出现错误，请检查！");
            }
        }
    }

    private void handleCsumFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "csum(")) {
            String csum = ExpressUtils.extractFuncContent(express, "csum(");
            if (StringUtils.isNotEmpty(csum)) {
                Matcher csumCMatcher = expressCPattern.matcher(csum);
                String cName = "";
                String sumColumnName = "";
                if (csumCMatcher.find()) {
                    cName = csumCMatcher.group(1);
                    sumColumnName = csumCMatcher.group(2);
                }

                Integer cTableId = getTableByName(cName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + cTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(cTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("csum函数中：" + sumColumnName + "未找到！");
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("csum函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                Integer tableLevel = tableVo.getTableLevel();
                List<String> codes = new ArrayList<>(tableLevel);
                for (int i = 0; i < tableLevel; i++) {
                    codes.add(tableVo.getTableMetaJson().get(i).getCode());
                }
                String codeString = String.join(",", codes);
                String query = String.format("select %s, sum(%s) as s from %s group by %s", codeString, sumColumnCode, tableName, codeString);
                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> csumCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    List<String> keyList = new ArrayList<>(tableLevel);
                    for (int i = 0; i < tableLevel; i++) {
                        keyList.add(MapUtils.getString(map, codes.get(i)));
                    }
                    csumCache.put(String.join("_", keyList), MapUtils.getObject(map, "s"));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CSUM + ruleId + "_" + cName, csumCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_C_REF + ruleId, Boolean.FALSE);

            } else {
                throw new RuleExecutionException("csum函数定义出现错误，请检查！");
            }
        }
    }

    private void handleVavgFunc(String express, Integer tableId, String columnCode) {
        // 平均
        if (StringUtils.contains(express, "vavg(")) {
            String vavg = ExpressUtils.extractFuncContent(express, "vavg(");
            if (StringUtils.isNotEmpty(vavg)) {
                Matcher vavgTMatcher = expressTPattern.matcher(vavg);
                String tName = "";
                String avgColumnName = "";
                if (vavgTMatcher.find()) {
                    tName = vavgTMatcher.group(1);
                    avgColumnName = vavgTMatcher.group(2);
                }

                Integer tTableId = getTableByName(tName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tTableId);
                String avgColumnCode = MapUtils.getString(columnNameCodeMap, avgColumnName);
                if (StringUtils.isEmpty(avgColumnCode)) {
                    throw new RuleExecutionException("vavg函数中：" + avgColumnName + "未找到！");
                }

                List<String> groupColumnNames = new ArrayList<>();
                while (vavgTMatcher.find()) {
                    if (!StringUtils.equals(vavgTMatcher.group(1), tName)) {
                        throw new RuleExecutionException("vavg函数中他表需要保持一致");
                    }
                    groupColumnNames.add(vavgTMatcher.group(2));
                }

                String avgColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(avgColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    avgColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    avgColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    avgColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(avgColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("vavg函数中：" + avgColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                if (CollectionUtils.isNotEmpty(groupColumnNames)) {
                    List<String> groupColumnCodes = new ArrayList<>(groupColumnNames.size());
                    for (String groupColumnName : groupColumnNames) {
                        String groupColumnCode = MapUtils.getString(columnNameCodeMap, groupColumnName);
                        if (StringUtils.isEmpty(groupColumnCode)) {
                            throw new RuleExecutionException("vavg函数中：" + groupColumnName + "未找到！");
                        } else {
                            groupColumnCodes.add(groupColumnCode);
                        }
                    }
                    String groupColumnCodesString1 = String.join(",", groupColumnCodes);
                    String query = String.format("select %s, avg(%s) as dcube_avg_a from %s group by %s", groupColumnCodesString1, avgColumnCode, tableName, groupColumnCodesString1);
                    Map<String, Object> vavgCache = tarRepository.executeQuerySql(query, groupColumnCodes, "dcube_avg_a");
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VAVG_T + ruleId, vavgCache);
                } else {
                    String query = String.format("select avg(%s) as s from %s", avgColumnCode, tableName);
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VAVG_T + ruleId, MapUtils.getObject(map, "s"));
                    }
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vavg函数定义出现错误，请检查！");
            }
        }
    }

    private void handleVavgwFunc(String express, Integer tableId, String columnCode) {
        // 加权平均
        if (StringUtils.contains(express, "vavgw(")) {
            String vavgw = ExpressUtils.extractFuncContent(express, "vavgw(");
            if (StringUtils.isNotEmpty(vavgw)) {
                Matcher vavgwTMatcher = expressTPattern.matcher(vavgw);
                String tName = "";
                String valueColumnName = "";
                String weightColumnName = "";
                if (vavgwTMatcher.find()) {
                    tName = vavgwTMatcher.group(1);
                    valueColumnName = vavgwTMatcher.group(2);
                }
                if (vavgwTMatcher.find()) {
                    tName = vavgwTMatcher.group(1);
                    weightColumnName = vavgwTMatcher.group(2);
                }

                Integer tTableId = getTableByName(tName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tTableId);
                String valueColumnCode = MapUtils.getString(columnNameCodeMap, valueColumnName);
                if (StringUtils.isEmpty(valueColumnCode)) {
                    throw new RuleExecutionException("vavgw函数中：" + valueColumnName + "未找到！");
                }
                String weightColumnCode = MapUtils.getString(columnNameCodeMap, weightColumnName);
                if (StringUtils.isEmpty(valueColumnCode)) {
                    throw new RuleExecutionException("vavgw函数中：" + weightColumnName + "未找到！");
                }

                List<String> groupColumnNames = new ArrayList<>();
                while (vavgwTMatcher.find()) {
                    if (!StringUtils.equals(vavgwTMatcher.group(1), tName)) {
                        throw new RuleExecutionException("vavgw函数中他表需要保持一致");
                    }
                    groupColumnNames.add(vavgwTMatcher.group(2));
                }

                String avgColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(valueColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    avgColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    avgColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    avgColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(avgColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("vavgw函数中：" + valueColumnName + "非数字类型不参与计算！");
                }
                statColumnMetaJson = tableMetaJsonMap.get(weightColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    avgColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    avgColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    avgColumnType = "VARCHAR";
                }
                cl = MemGridUtils.getColumnType(avgColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("vavgw函数中：" + weightColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                if (CollectionUtils.isNotEmpty(groupColumnNames)) {
                    List<String> groupColumnCodes = new ArrayList<>(groupColumnNames.size());
                    for (String groupColumnName : groupColumnNames) {
                        String groupColumnCode = MapUtils.getString(columnNameCodeMap, groupColumnName);
                        if (StringUtils.isEmpty(groupColumnCode)) {
                            throw new RuleExecutionException("vavgw函数中：" + groupColumnName + "未找到！");
                        } else {
                            groupColumnCodes.add(groupColumnCode);
                        }
                    }

                    String groupColumnCodesString1 = String.join(",", groupColumnCodes);
                    String query = String.format("SELECT %s,CASE WHEN SUM(COALESCE(%s, 0)) = 0 THEN 0" +
                            "ELSE SUM(COALESCE(%s, 0) * COALESCE(%s, 0)) / SUM(COALESCE(%s, 0))" +
                            "END AS dcube_weighted_average FROM %s group by %s", groupColumnCodesString1, weightColumnCode, valueColumnCode, weightColumnCode, weightColumnCode, tableName, groupColumnCodesString1);
                    Map<String, Object> vavgwCache = tarRepository.executeQuerySql(query, groupColumnCodes, "dcube_weighted_average");
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VAVGW_T + ruleId, vavgwCache);
                } else {
                    String query = String.format("SELECT CASE WHEN SUM(COALESCE(%s, 0)) = 0 THEN 0" +
                            "ELSE SUM(COALESCE(%s, 0) * COALESCE(%s, 0)) / SUM(COALESCE(%s, 0))" +
                            "END AS dcube_weighted_average FROM %s", weightColumnCode, valueColumnCode, weightColumnCode, weightColumnCode, tableName);
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VAVGW_T + ruleId, MapUtils.getObject(map, "dcube_weighted_average"));
                    }
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vavgw函数定义出现错误，请检查！");
            }
        }
    }

    private void handleVlookupFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "vlookup(")) {
            String vlookup = ExpressUtils.extractFuncContent(express, "vlookup(");
            if (StringUtils.isNotEmpty(vlookup)) {
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getIntValue(columnIndexMap, columnCode));
//                int oColumnCodeIndex = MapUtils.getIntValue(columnIndexMap, oColumnCode);
//                if (oColumnCodeIndex == 0) {
//                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VLOOKUP_O_ID + ruleId, Boolean.TRUE);
//                }
                List<String> tColumnNameList = new ArrayList<>(2);
                Matcher vlookupTMatcher = expressTPattern.matcher(vlookup);
                String tName = "";
                while (vlookupTMatcher.find()) {
                    if (StringUtils.isEmpty(tName)) {
                        tName = vlookupTMatcher.group(1);
                    } else if (!StringUtils.equals(tName, vlookupTMatcher.group(1))) {
                        throw new RuleExecutionException("vlookup函数中他表需一致！");
                    }
                    tColumnNameList.add(vlookupTMatcher.group(2));
                }
                List<String> tColumnCodeList = new ArrayList<>(2);
                Table tTable = getTableByName(tName);
                Integer tTableId = tTable.getId();
                Map tColumnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);
                tColumnNameList.forEach(tColumnName -> {
                    String tColumnCode = MapUtils.getString(tColumnNameCodeMap, tColumnName);
                    if (StringUtils.isEmpty(tColumnCode)) {
                        throw new RuleExecutionException("vlookup函数中：" + tColumnName + "未找到！");
                    }
                    tColumnCodeList.add(tColumnCode);
                });
//                Map<String, Integer> tColumnIndexMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + tTableId);
//                int tColumnCodeIndex = MapUtils.getIntValue(tColumnIndexMap, tColumnCode);
//                if (tColumnCodeIndex == 0) {
//                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VLOOKUP_T_ID_FLAG + ruleId, Boolean.TRUE);
//                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VLOOKUP_T_ID_COLUMN_NAME + ruleId, tColumnName);
//                }
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);

                String tableName = "grid." + tTable.getMemTableName().toLowerCase();
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                // 判断没有本表条件就是常量
                Matcher vlookupOMatcher = expressOPattern.matcher(vlookup);
                // 本表参数的code
                boolean foundO = false;
                String oColumnCode;
                String _oColumnCode;
                if (vlookupOMatcher.find()) {
                    String columnName = vlookupOMatcher.group(1);
                    Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tableId);
                    oColumnCode = MapUtils.getString(columnNameCodeMap, columnName);
                    if (StringUtils.isEmpty(oColumnCode)) {
                        throw new RuleExecutionException("vlookup函数的本表列中：" + columnName + "未找到！");
                    }
                    _oColumnCode = oColumnCode;
                    foundO = true;
                } else {
                    // 截取出常量条件
                    // 查找最后一个右括号的位置
                    int lastParenIndex = express.lastIndexOf(")");
                    // 从最后一个右括号到其前一个逗号位置截取
                    int lastCommaIndex = express.lastIndexOf(",", lastParenIndex);
                    oColumnCode = express.substring(lastCommaIndex + 1, lastParenIndex).trim();
                    oColumnCode = StringUtils.removeStart(oColumnCode, "\"");
                    oColumnCode = StringUtils.removeEnd(oColumnCode, "\"");
                    _oColumnCode = "'" + oColumnCode + "'";
                }

                // join
                String query = foundO ?
                        String.format("SELECT %s FROM %s t LEFT JOIN %s o ON %s",
                                "t." + tColumnCodeList.get(0) + ",t." + tColumnCodeList.get(1),
                                tableName,
                                "grid." + tableVo.getMemTableName().toLowerCase(),
                                "o." + _oColumnCode + "=" + "t." + tColumnCodeList.get(1)) :
                        String.format("SELECT %s FROM %s t where %s",
                                "t." + tColumnCodeList.get(0),
                                tableName,
                                _oColumnCode + "=" + "t." + tColumnCodeList.get(1));

                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> vlookupCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    String key;
                    if (foundO) {
                        Object object = MapUtils.getObject(map, tColumnCodeList.get(1));
                        if (object == null) {
                            continue;
                        }
                        if (object instanceof Date) {
                            key = DateUtil.formatDate((Date) object);
                        } else if (object instanceof String) {
                            key = (String) object;
                        } else {
                            key = String.valueOf(object);
                        }
                    } else {
                        key = oColumnCode;
                    }

                    vlookupCache.put(key, map.get(tColumnCodeList.get(0)));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VLOOKUP_T + ruleId, vlookupCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vlookup函数定义出现错误，请检查！");
            }
        }
    }

    private void handleVlookupsFunc(String express, Integer tableId, String columnCode) {
        if (StringUtils.contains(express, "vlookups(")) {
            String vlookups = ExpressUtils.extractFuncContent(express, "vlookups(");
            if (StringUtils.isNotEmpty(vlookups)) {
                List<String> args = extractPairs(vlookups);
                List<Boolean> findOList = new ArrayList<>(args.size() / 2);
                List<String> columnNameList = new ArrayList<>();
                for (int i = 2, size = args.size(); i < size; i += 2) {
                    Matcher vlookupsOMatcher = expressOPattern.matcher(args.get(i));
                    if (vlookupsOMatcher.find()) {
                        columnNameList.add(vlookupsOMatcher.group(1));
                        findOList.add(Boolean.TRUE);
                    } else {
                        String arg = args.get(i);
                        arg = StringUtils.removeStart(arg, "\"");
                        arg = StringUtils.removeEnd(arg, "\"");
                        columnNameList.add(arg);
                        findOList.add(Boolean.FALSE);
                    }
                }
                List<String> tColumnNameList = new ArrayList<>(columnNameList.size() + 1);
                String tName;
                // 第一个是取值的
                Matcher vlookupsTMatcher = expressTPattern.matcher(args.get(0));
                if (vlookupsTMatcher.find()) {
                    tName = vlookupsTMatcher.group(1);
                    tColumnNameList.add(vlookupsTMatcher.group(2));
                } else {
                    throw new RuleExecutionException(String.format("vlookups函数中他表条件【%s】错误！", args.get(0)));
                }
                for (int i = 1, size = args.size(); i < size; i += 2) {
                    vlookupsTMatcher = expressTPattern.matcher(args.get(i));
                    if (vlookupsTMatcher.find()) {
                        if (!StringUtils.equals(tName, vlookupsTMatcher.group(1))) {
                            throw new RuleExecutionException(String.format("vlookups函数中他表【%s】&【%s】需一致！", tName, vlookupsTMatcher.group(1)));
                        }
                        tColumnNameList.add(vlookupsTMatcher.group(2));
                    } else {
                        throw new RuleExecutionException(String.format("vlookups函数中他表条件【%s】错误！", args.get(i)));
                    }
                }
                List<String> columnCodeList = new ArrayList<>(columnNameList.size());
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tableId);
                for (int i = 0, size = columnNameList.size(); i < size; i++) {
                    String columnName = columnNameList.get(i);
                    Boolean findO = findOList.get(i);
                    String oColumnCode;
                    if (Boolean.TRUE.equals(findO)) {
                        // 本表参数的code
                        oColumnCode = MapUtils.getString(columnNameCodeMap, columnName);
                        if (StringUtils.isEmpty(oColumnCode)) {
                            throw new RuleExecutionException("vlookups函数的本表列中：" + columnName + "未找到！");
                        }
                    } else {
                        oColumnCode = columnName;
                    }
                    columnCodeList.add(oColumnCode);
                }

                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getIntValue(columnIndexMap, columnCode));
//                int oColumnCodeIndex = MapUtils.getIntValue(columnIndexMap, oColumnCode);
//                if (oColumnCodeIndex == 0) {
//                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.vlookups_O_ID + ruleId, Boolean.TRUE);
//                }
                String tColumnCode_ = "";
                int columnCodeListSize = columnCodeList.size();
                List<String> tColumnCodeList = new ArrayList<>(columnCodeListSize);
                Table tTable = getTableByName(tName);
                Integer tTableId = tTable.getId();
                Map tColumnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);
                for (int i = 0; i < tColumnNameList.size(); i++) {
                    String tColumnName = tColumnNameList.get(i);
                    String tColumnCode = MapUtils.getString(tColumnNameCodeMap, tColumnName);
                    if (StringUtils.isEmpty(tColumnCode)) {
                        throw new RuleExecutionException("vlookups函数中：" + tColumnName + "未找到！");
                    }
                    if (i == 0) {
                        tColumnCode_ = tColumnCode;
                    } else {
                        tColumnCodeList.add(tColumnCode);
                    }
                }

//                Map<String, Integer> tColumnIndexMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + tTableId);
//                int tColumnCodeIndex = MapUtils.getIntValue(tColumnIndexMap, tColumnCode);
//                if (tColumnCodeIndex == 0) {
//                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.vlookups_T_ID_FLAG + ruleId, Boolean.TRUE);
//                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.vlookups_T_ID_COLUMN_NAME + ruleId, tColumnName);
//                }
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);

                String tableName = "grid." + tTable.getMemTableName().toLowerCase();
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                // join
                List<String> oColumnCodelist = new ArrayList<>(columnCodeListSize);
                for (int i = 0; i < columnCodeListSize; i++) {
                    String s = columnCodeList.get(i);
                    if (findOList.get(i)) {
                        oColumnCodelist.add("o." + s);
                    } else {
                        oColumnCodelist.add(s);
                    }
                }

                List<String> tColumnCodeList_ = tColumnCodeList.stream().map(s -> "t." + s).collect(Collectors.toList());
                StringBuilder onConditionSql = new StringBuilder();
                StringBuilder whereConditionSql = new StringBuilder();
                for (int i = 0, size = oColumnCodelist.size(); i < size; i++) {
                    if (Boolean.TRUE.equals(findOList.get(i))) {
                        onConditionSql.append(oColumnCodelist.get(i)).append("=").append(tColumnCodeList_.get(i));
                        if (i != size - 1) {
                            onConditionSql.append(" AND ");
                        }
                    } else {
                        String oColumnCode = oColumnCodelist.get(i);
                        oColumnCode = StringUtils.removeStart(oColumnCode, "\"");
                        oColumnCode = StringUtils.removeEnd(oColumnCode, "\"");
                        oColumnCode = "'" + oColumnCode + "'";
                        whereConditionSql.append(oColumnCode).append("=").append(tColumnCodeList_.get(i));
                        if (i != size - 1) {
                            whereConditionSql.append(" AND ");
                        }
                    }
                }
                String onConditionSqlString = onConditionSql.toString();
                String whereConditionSqlString = whereConditionSql.toString();
                String query;
                if (StringUtils.isEmpty(whereConditionSqlString)) {
                    query = String.format("SELECT %s FROM %s t LEFT JOIN %s o ON %s",
                            String.join(",", tColumnCodeList_) + "," + "t." + tColumnCode_,
                            tableName,
                            "grid." + tableVo.getMemTableName().toLowerCase(),
                            StringUtils.removeEndIgnoreCase(StringUtils.trim(onConditionSqlString), "AND"));
                } else {
                    if (StringUtils.isEmpty(onConditionSqlString)) {
                        // 全部是where条件
                        query = String.format("SELECT %s FROM %s t where %s",
                                "t." + tColumnCode_,
                                tableName,
                                StringUtils.removeEndIgnoreCase(StringUtils.trim(whereConditionSqlString), "AND"));
                    } else {
                        StringBuilder tColumnCodeString = new StringBuilder();
                        for (int i = 0, size = tColumnCodeList_.size(); i < size; i++) {
                            if (findOList.get(i)) {
                                tColumnCodeString.append(tColumnCodeList_.get(i));
                            }
                            if (i != size - 1) {
                                if (StringUtils.isNotEmpty(tColumnCodeString)) {
                                    tColumnCodeString.append(",");
                                }
                            }
                        }
                        // 部分on条件
                        query = String.format("SELECT %s FROM (%s) t LEFT JOIN %s o ON %s",
                                StringUtils.removeEndIgnoreCase(tColumnCodeString.toString(), ",") + ",t." + tColumnCode_,
                                "select t.* from " + tableName + " t where " + StringUtils.removeEndIgnoreCase(StringUtils.trim(whereConditionSqlString), "AND"),
                                "grid." + tableVo.getMemTableName().toLowerCase(),
                                StringUtils.removeEndIgnoreCase(StringUtils.trim(onConditionSqlString), "AND"));
                    }
                }
                List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                Map<String, Object> vlookupsCache = Maps.newHashMapWithExpectedSize(data.size());
                for (Map<String, Object> map : data) {
                    StringBuilder keyBuilder = new StringBuilder();
                    for (int i = 0, size = tColumnCodeList.size(); i < size; i++) {
                        String tColumnCode = tColumnCodeList.get(i);
                        if (findOList.get(i)) {
                            Object object = MapUtils.getObject(map, tColumnCode);
                            if (object == null) {
                                continue;
                            }
                            if (object instanceof Date) {
                                keyBuilder.append(DateUtil.formatDate((Date) object));
                            } else if (object instanceof String) {
                                keyBuilder.append((String) object);
                            } else {
                                keyBuilder.append(object);
                            }
                        } else {
                            keyBuilder.append(columnCodeList.get(i));
                        }
                        if (i != size - 1) {
                            keyBuilder.append("_");
                        }
                    }
                    vlookupsCache.put(keyBuilder.toString(), map.get(tColumnCode_));
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VLOOKUPS_T + ruleId, vlookupsCache);
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vlookups函数定义出现错误，请检查！");
            }
        }
    }

    public static List<String> extractPairs(String input) {
        // 分割参数
        return splitArgs(input);
    }

    private static List<String> splitArgs(String argsStr) {
        List<String> args = new ArrayList<>();
        int depth = 0;
        int start = 0;
        for (int i = 0; i < argsStr.length(); i++) {
            char c = argsStr.charAt(i);
            if (c == '(') {
                depth++;
            } else if (c == ')') {
                depth--;
            } else if (c == ',' && depth == 0) {
                args.add(argsStr.substring(start, i).trim());
                start = i + 1;
            }
        }
        args.add(argsStr.substring(start).trim());
        return args;
    }

    private void handleVminFunc(String express, Integer tableId, String columnCode) {
        // 最小值
        if (StringUtils.contains(express, "vmin(")) {
            String vminExpress = ExpressUtils.extractFuncContent(express, "vmin(");
            if (StringUtils.isNotEmpty(vminExpress)) {
                Matcher minTMatcher = expressTPattern.matcher(vminExpress);
                String tName = "";
                String minColumnName = "";
                if (minTMatcher.find()) {
                    tName = minTMatcher.group(1);
                    minColumnName = minTMatcher.group(2);
                }
                if (StringUtils.isEmpty(tName)) {
                    throw new RuleExecutionException("vmin函数未找到他表，请检查！");
                }
                if (StringUtils.isEmpty(minColumnName)) {
                    throw new RuleExecutionException("vmin函数计算的最小值列为空，请检查！");
                }

                Table tableByName = getTableByName(tName);
                Integer tTableId = tableByName.getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);
                String minColumnCode = MapUtils.getString(columnNameCodeMap, minColumnName);
                if (StringUtils.isEmpty(minColumnCode)) {
                    throw new RuleExecutionException("vmin函数计算的最小值列" + minColumnCode + "未找到，请检查！");
                }

                Map<String, List<String>> minTMap = Maps.newHashMapWithExpectedSize(1);
                while (minTMatcher.find()) {
                    if (!StringUtils.equals(minTMatcher.group(1), tName)) {
                        throw new RuleExecutionException("vmin函数中他表需要保持一致");
                    }
                    String columnName = minTMatcher.group(2);
                    List<String> columns = minTMap.computeIfAbsent(tName, v -> new ArrayList<>());
                    columns.add(columnName);
                }

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                String tableName = "grid." + tableByName.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                List<String> columns = MapUtils.getObject(minTMap, tName);
                if (CollectionUtils.isEmpty(columns)) {
                    String query = "select min(" + minColumnCode + ") m" + " from  " + tableName;
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.MIN_T + ruleId, MapUtils.getObject(map, "m"));
                    }
                } else {
                    List<String> columnCodes = new ArrayList<>(columns.size());
                    for (String column : columns) {
                        String minColumnCode1 = MapUtils.getString(columnNameCodeMap, column);
                        if (StringUtils.isEmpty(minColumnCode1)) {
                            throw new RuleExecutionException("vmin函数中：" + column + "未找到！");
                        }
                        columnCodes.add(minColumnCode1);
                    }

                    StringBuilder selectBuilder = new StringBuilder();
                    StringBuilder fromBuilder = new StringBuilder();
                    for (int i = 0, len = columnCodes.size(); i < len; i++) {
                        String c = columnCodes.get(i);
                        selectBuilder.append("a.").append(c);
                        fromBuilder.append("a.").append(c).append("=b.").append(c);
                        if (i != len - 1) {
                            selectBuilder.append(",");
                            fromBuilder.append(" and ");
                        }
                    }
                    String querySelect = selectBuilder.toString();
                    String columnString = StringUtils.trim(fromBuilder.toString());
                    String query = "select a." + minColumnCode + "," + querySelect + " from  " + tableName + " a where a." +
                            minColumnCode + " = (select min(b." + minColumnCode + ") from " + tableName + " b where " + columnString + ")";
                    Map<String, Object> minCache = tarRepository.executeQuerySql(query, columnCodes, minColumnCode);
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.MIN_T + ruleId, minCache);
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vmin规则定义的参数格式不正确，请检查！");
            }
        }
    }

    private void handleVmaxFunc(String express, Integer tableId, String columnCode) {
        // 最大值
        if (StringUtils.contains(express, "vmax(")) {
            String vmaxExpress = ExpressUtils.extractFuncContent(express, "vmax(");
            if (StringUtils.isNotEmpty(vmaxExpress)) {
                Matcher maxTMatcher = expressTPattern.matcher(vmaxExpress);
                String tName = "";
                String maxColumnName = "";
                if (maxTMatcher.find()) {
                    tName = maxTMatcher.group(1);
                    maxColumnName = maxTMatcher.group(2);
                }
                if (StringUtils.isEmpty(tName)) {
                    throw new RuleExecutionException("vmax函数未找到他表，请检查！");
                }
                if (StringUtils.isEmpty(maxColumnName)) {
                    throw new RuleExecutionException("vmax函数计算的最大值列为空，请检查！");
                }

                Table tableByName = getTableByName(tName);
                Integer tTableId = tableByName.getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);
                String maxColumnCode = MapUtils.getString(columnNameCodeMap, maxColumnName);
                if (StringUtils.isEmpty(maxColumnCode)) {
                    throw new RuleExecutionException("vmax函数计算的最大值列" + maxColumnCode + "未找到，请检查！");
                }

                Map<String, List<String>> maxTMap = Maps.newHashMapWithExpectedSize(1);
                while (maxTMatcher.find()) {
                    if (!StringUtils.equals(maxTMatcher.group(1), tName)) {
                        throw new RuleExecutionException("vmax函数中他表需要保持一致");
                    }
                    String columnName = maxTMatcher.group(2);
                    List<String> columns = maxTMap.computeIfAbsent(tName, v -> new ArrayList<>());
                    columns.add(columnName);
                }

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                String tableName = "grid." + tableByName.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                List<String> columns = MapUtils.getObject(maxTMap, tName);
                if (CollectionUtils.isEmpty(columns)) {
                    String query = "select max(" + maxColumnCode + ") m" + " from  " + tableName;
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.MAX_T + ruleId, MapUtils.getObject(map, "m"));
                    }
                } else {
                    List<String> columnCodes = new ArrayList<>(columns.size());
                    for (String column : columns) {
                        String maxColumnCode1 = MapUtils.getString(columnNameCodeMap, column);
                        if (StringUtils.isEmpty(maxColumnCode1)) {
                            throw new RuleExecutionException("vmax函数中：" + column + "未找到！");
                        }
                        columnCodes.add(maxColumnCode1);
                    }

                    StringBuilder selectBuilder = new StringBuilder();
                    StringBuilder fromBuilder = new StringBuilder();
                    for (int i = 0, len = columnCodes.size(); i < len; i++) {
                        String c = columnCodes.get(i);
                        selectBuilder.append("a.").append(c);
                        fromBuilder.append("a.").append(c).append("=b.").append(c);
                        if (i != len - 1) {
                            selectBuilder.append(",");
                            fromBuilder.append(" and ");
                        }
                    }
                    String querySelect = selectBuilder.toString();
                    String columnString = StringUtils.trim(fromBuilder.toString());

                    String query = "select a." + maxColumnCode + "," + querySelect + " from  " + tableName + " a where a." +
                            maxColumnCode + " = (select max(b." + maxColumnCode + ") from " + tableName + " b where " + columnString + ")";
                    Map<String, Object> maxCache = tarRepository.executeQuerySql(query, columnCodes, maxColumnCode);
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.MAX_T + ruleId, maxCache);
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vmax规则定义的参数格式不正确，请检查！");
            }
        }
    }

    private void handleVcountFunc(String express, Integer tableId, String columnCode) {
        // 计数
        if (StringUtils.contains(express, "vcount(")) {
            String vcountExpress = ExpressUtils.extractFuncContent(express, "vcount(");
            if (StringUtils.isNotEmpty(vcountExpress)) {
                Matcher vcountTMatcher = expressTPattern.matcher(vcountExpress);
                String tName = "";
                String vcountColumnName = "";
                if (vcountTMatcher.find()) {
                    tName = vcountTMatcher.group(1);
                    vcountColumnName = vcountTMatcher.group(2);
                }
                if (StringUtils.isEmpty(tName)) {
                    throw new RuleExecutionException("vcount函数未找到他表，请检查！");
                }
                if (StringUtils.isEmpty(vcountColumnName)) {
                    throw new RuleExecutionException("vcount函数计数的列为空，请检查！");
                }
                Table tableByName = getTableByName(tName);
                Integer tTableId = tableByName.getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);
                String vcountColumnCode = MapUtils.getString(columnNameCodeMap, vcountColumnName);
                if (StringUtils.isEmpty(vcountColumnCode)) {
                    throw new RuleExecutionException("vcount函数计数的列" + vcountColumnCode + "未找到，请检查！");
                }

                Map<String, List<String>> vcountTMap = Maps.newHashMapWithExpectedSize(1);
                while (vcountTMatcher.find()) {
                    if (!StringUtils.equals(vcountTMatcher.group(1), tName)) {
                        throw new RuleExecutionException("vcount函数中他表需要保持一致");
                    }
                    String sumColumnName = vcountTMatcher.group(2);
                    List<String> columns = vcountTMap.computeIfAbsent(tName, v -> new ArrayList<>());
                    columns.add(sumColumnName);
                }

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                String tableName = "grid." + tableByName.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                List<String> columns = MapUtils.getObject(vcountTMap, tName);
                if (CollectionUtils.isEmpty(columns)) {
                    String query = "select count(" + vcountColumnCode + ") as c from " + tableName;
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COUNT_T + ruleId, MapUtils.getObject(map, "c"));
                    }
                } else {
                    List<String> groupColumnCodes = new ArrayList<>(columns.size());
                    for (String groupColumnName : columns) {
                        String groupColumnCode = MapUtils.getString(columnNameCodeMap, groupColumnName);
                        if (StringUtils.isEmpty(groupColumnCode)) {
                            throw new RuleExecutionException("vcount函数中：" + groupColumnName + "未找到！");
                        } else {
                            groupColumnCodes.add(groupColumnCode);
                        }
                    }

                    String columnString = String.join(",", groupColumnCodes);

                    String query = "select count(*) as dcube_count_c," + columnString + " from " + tableName + " group by " + columnString;
                    Map<String, Object> vcountCache = tarRepository.executeQuerySql(query, groupColumnCodes, "dcube_count_c");
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COUNT_T + ruleId, vcountCache);
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vcount规则定义的参数格式不正确，请检查！");
            }
        }
    }

    private void handleVsumFunc(String express, Integer tableId, String columnCode) {
        // 聚合
        if (StringUtils.contains(express, "vsum(")) {
            String vsum = ExpressUtils.extractFuncContent(express, "vsum(");
            if (StringUtils.isNotEmpty(vsum)) {
                Matcher vsumTMatcher = expressTPattern.matcher(vsum);
                String tName = "";
                String sumColumnName = "";
                if (vsumTMatcher.find()) {
                    tName = vsumTMatcher.group(1);
                    sumColumnName = vsumTMatcher.group(2);
                }

                Integer tTableId = getTableByName(tName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);

                Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("vsum函数中：" + sumColumnName + "未找到！");
                }

                List<String> groupColumnNames = new ArrayList<>();
                while (vsumTMatcher.find()) {
                    if (!StringUtils.equals(vsumTMatcher.group(1), tName)) {
                        throw new RuleExecutionException("vsum函数中他表需要保持一致");
                    }
                    groupColumnNames.add(vsumTMatcher.group(2));
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("vsum函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                if (CollectionUtils.isNotEmpty(groupColumnNames)) {
                    List<String> groupColumnCodes = new ArrayList<>(groupColumnNames.size());
                    for (String groupColumnName : groupColumnNames) {
                        String groupColumnCode = MapUtils.getString(columnNameCodeMap, groupColumnName);
                        if (StringUtils.isEmpty(groupColumnCode)) {
                            throw new RuleExecutionException("vsum函数中：" + groupColumnName + "未找到！");
                        } else {
                            groupColumnCodes.add(groupColumnCode);
                        }
                    }

                    String groupColumnCodesString1 = String.join(",", groupColumnCodes);
                    String query = String.format("select %s, sum(%s) as dcube_sum_s from %s group by %s", groupColumnCodesString1, sumColumnCode, tableName, groupColumnCodesString1);
                    Map<String, Object> vsumCache = tarRepository.executeQuerySql(query, groupColumnCodes, "dcube_sum_s");
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VSUM_T_SUM + ruleId, vsumCache);
                } else {
                    String query = String.format("select sum(%s) as s from %s", sumColumnCode, tableName);
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VSUM_T_SUM + ruleId, MapUtils.getObject(map, "s"));
                    }
                }
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLLECT_T_REF + ruleId, Boolean.FALSE);
            } else {
                throw new RuleExecutionException("vsum函数定义出现错误，请检查！");
            }
        }
    }

    private void handleAllocaFunc(String express, Integer tableId, String columnCode) {
        // 分摊
        if (StringUtils.contains(express, "alloca(")) {
            String alloca = ExpressUtils.extractFuncContent(express, "alloca(");
            if (StringUtils.isNotEmpty(alloca)) {
                alloca = RegExUtils.removePattern(alloca, expressTPatternString);
                String[] split = alloca.split(",");
                if (split.length == 1 || split.length == 4) {
                    Matcher oMatcher = expressOPattern.matcher(split[0]);
                    boolean flag1 = false;
                    String columnName;
                    if (oMatcher.find()) {
                        flag1 = true;
                        columnName = oMatcher.group(1);
                    } else {
                        columnName = split[0].replace("\"", "");
                    }
                    if (StringUtils.isEmpty(columnName)) {
                        throw new RuleExecutionException("alloca函数分摊的本表列为空，请检查！");
                    }

                    Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tableId);
                    Map<Integer, Long> indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
                    Map<String, Integer> columnIndexMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP);
                    Long ruleId = MapUtils.getLong(indexRuleIdMap, MapUtils.getInteger(columnIndexMap, columnCode));
                    TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
                    String allocColumnCode = MapUtils.getString(columnNameCodeMap, columnName);
                    if (flag1 && StringUtils.isEmpty(allocColumnCode)) {
                        throw new RuleExecutionException("alloca函数中：" + columnName + "未找到！");
                    }
                    String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                    // group by
                    AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                    if (split.length == 4) {
                        oMatcher = expressOPattern.matcher(split[3]);
                        String statColumnName = "";
                        if (oMatcher.find()) {
                            statColumnName = oMatcher.group(1);
                        }
                        String statColumnCode = MapUtils.getString(columnNameCodeMap, statColumnName);
                        if (StringUtils.isNotEmpty(statColumnName)) {
                            if (StringUtils.isEmpty(statColumnCode)) {
                                throw new RuleExecutionException("alloca函数中：" + statColumnName + "未找到！");
                            }
                            String statColumnType;
                            Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                            TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(statColumnCode);
                            // 为兼容数据格式 优先赋值存储格式
                            if (statColumnMetaJson.getDataFormat() != null) {
                                statColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                            } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                                statColumnType = statColumnMetaJson.getNewColumnType();
                            } else {
                                statColumnType = "VARCHAR";
                            }
                            Class cl = MemGridUtils.getColumnType(statColumnType);
                            // 非数字类型不参与计算
                            if (Integer.class != cl && Double.class != cl) {
                                throw new RuleExecutionException("alloca函数中：" + statColumnName + "非数字类型不参与计算！");
                            }
                        }
                        // 按权重
                        String query;
                        if (flag1) {
                            query = String.format("select %s as n, sum(%s) as s from %s group by %s", allocColumnCode, statColumnCode, tableName, allocColumnCode);
                        } else {
                            query = String.format("select sum(%s) as s from %s", statColumnCode, tableName);
                        }
                        List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                        Map<String, Object> allocaCache = Maps.newHashMapWithExpectedSize(data.size());
                        for (Map<String, Object> map : data) {
                            Object object = MapUtils.getObject(map, allocColumnCode);
                            String objectVal = "";
                            if (object instanceof Date) {
                                objectVal = DateUtil.formatDate((Date) object);
                            } else {
                                objectVal = String.valueOf(object);
                            }
                            allocaCache.put((flag1 ? objectVal : columnName), MapUtils.getObject(map, "s"));
                        }
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.ALLOCA_O_SUM + ruleId, allocaCache);
                    } else {
                        // 均摊
                        String query;
                        if (flag1) {
                            query = String.format("select %s as n, count(*) as c from %s group by %s", allocColumnCode, tableName, allocColumnCode);
                        } else {
                            query = String.format("select count(*) as c from %s", tableName);
                        }
                        List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                        Map<String, Object> allocaCache = Maps.newHashMapWithExpectedSize(data.size());
                        for (Map<String, Object> map : data) {
                            Object object = MapUtils.getObject(map, allocColumnCode);
                            String objectVal = "";
                            if (object instanceof Date) {
                                objectVal = DateUtil.formatDate((Date) object);
                            } else {
                                objectVal = String.valueOf(object);
                            }
                            allocaCache.put(flag1 ? objectVal : columnName, MapUtils.getObject(map, "c"));
                        }
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.ALLOCA_O_COUNT + ruleId, allocaCache);
                    }
                } else {
                    throw new RuleExecutionException("alloc函数定义出现错误，请检查！");
                }
            } else {
                throw new RuleExecutionException("alloc函数定义出现错误，请检查！");
            }
        }

    }


    /**
     * 执行规则计算
     *
     * @param tableId
     * @param columnCodeList
     * @param tableData
     */
    @Override
    public void doExecuteRuleExpress(Integer tableId, List<String> columnCodeList, List<Object[]> tableData, ReportDto<AtomicLong> reportDto) {
        log.info("==================> start 执行表{}的规则，列：{}", tableId, columnCodeList);
        reportDto.setState(StateEnum.RUNNING.getCode());
        Map<Integer, Long> indexRuleIdMap = null;
        try {
            if (CollectionUtils.isEmpty(tableData) || CollectionUtils.isEmpty(columnCodeList)) {
                return;
            }
            initLocalCache(tableId, columnCodeList);
            Map<Integer, String> indexColumnMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP);
            Map<Integer, String> indexExpressMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_EXPRESS_MAP);
            indexRuleIdMap = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP);
            if (MapUtils.isNotEmpty(indexExpressMap)) {
                reportDto.setTaskName(reportDto.getTaskName() + "==>正在初始化计算规则缓存");
                for (Map.Entry<Integer, String> entry : indexExpressMap.entrySet()) {
                    initExpressCache(tableId, MapUtils.getString(indexColumnMap, entry.getKey()), entry.getValue());
                }
                reportDto.setTaskName(StringUtils.removeEnd(reportDto.getTaskName(), "==>正在初始化计算规则缓存"));

                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
                String memTableName = tableVo.getMemTableName();
                TableMetaData tableMetaData = MemGridUtils.getTableMetaData(memTableName);
                ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CURRENT_TABLE_ID, tableId);

                reportDto.setTotalRecord((long) tableData.size());
                reportDto.setCurrentRecord(new AtomicLong(0));
                TaskReport.put(reportDto);
                for (Object[] data : tableData) {
                    for (Map.Entry<Integer, String> entry : indexExpressMap.entrySet()) {
                        Integer index = entry.getKey();
                        Object computeValue = getComputeValue(MapUtils.getLong(indexRuleIdMap, index), entry.getValue(), data, MapUtils.getString(indexColumnMap, index));
                        data[index] = MemGridUtils.getValue(tableMetaData.getColumnTypes().get(index), computeValue);
                    }
                    ReportTool.appendRecord(reportDto, 1);
                }
            }
        } catch (Exception e) {
            log.error("执行规则计算时出现异常：", e);
            reportDto.setState(StateEnum.FAILED.getCode()).setStatus(TaskStatusEnums.ERROR);
            String message;
            if (e instanceof CompletionException) {
                message = ExceptionUtil.getLocalizedMessage(e.getCause());
            } else {
                message = ExceptionUtil.getLocalizedMessage(e);
            }
            reportDto.setMsg(message);
            throw new RuleExecutionException(message);
        } finally {
            log.info("==================> end 执行表{}的规则，列：{}", tableId, columnCodeList);
            TaskReport.finished(reportDto);
            removeFuncCache(indexRuleIdMap);
        }
    }

    private static void removeFuncCache(Map<Integer, Long> indexRuleIdMap) {
        if (MapUtils.isNotEmpty(indexRuleIdMap)) {
            indexRuleIdMap.values().forEach(v -> {
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.ALLOCA_O_COUNT + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VSUM_T_SUM + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.MAX_T + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.MIN_T + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.COUNT_T + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VAVGW_T + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VAVGW_T_2 + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VLOOKUP_T_ID_FLAG + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VLOOKUP_T_REVERSE_MAP + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VLOOKUP_T + v);
                ThreadLocalUtils.removeTtl(ExpressUtils.class, RuleConstant.VLOOKUPS_T + v);
            });
        }
    }

    /**
     * 执行规则计算，并获取返回值
     *
     * @param ruleExpress 计算规则
     * @param rowData
     * @param columnCode
     * @return
     * @throws Exception
     */
    private Object getComputeValue(Long ruleId, String ruleExpress, Object[] rowData, String columnCode) {
        if (StringUtils.isNotEmpty(ruleExpress)) {
            Map<String, Object> context = Maps.newHashMapWithExpectedSize(4);
            context.put(RuleOperatorEnum.O.name(), rowData);// 当前表字段及值
            context.put(RuleConstant.RULE_ID, ruleId);// 当前规则id
            context.put(RuleConstant.CURRENT_COLUMN_CODE, columnCode);// 当前列编码
            context.put(RuleConstant.RULE_EXPRESS, ruleExpress);// 规则
            return expressUtils.execute(ruleExpress, context);
        }
        return null;
    }

    /**
     * 根据子表id查询父表内存表名
     *
     * @param childTableId
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public String getParentMemTableName(Integer childTableId) {
        TableRel one = tableRelService.getOne(new LambdaQueryWrapper<TableRel>().eq(TableRel::getChildTableId, childTableId));
        if (one != null) {
            Table table = tableService.getOne(new LambdaQueryWrapper<Table>().eq(Table::getId, one.getParentTableId()));
            if (table != null) {
                return table.getMemTableName();
            }
        }
        return null;
    }

    /**
     * 根据子表id查询关联表
     *
     * @param childTableId
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public TableRel getTableRelByChildTableId(Integer childTableId) {
        return tableRelService.getOne(new LambdaQueryWrapper<TableRel>().eq(TableRel::getChildTableId, childTableId));
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public Table getTableById(Integer id) {
        return tableService.getById(id);
    }

    /**
     * @param id
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public TableVo getTableVoById(Integer id) {
        return tableService.get(id);
    }

    /**
     * @param ids
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public List<TableVo> getTableVoByIds(List<Integer> ids) {
        return tableService.getByIds(ids);
    }

    /**
     * 通过tableName查询
     *
     * @param tableName
     * @return
     */
    @Override
//    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public Table getTableByName(String tableName) {
        Object obj = cubeRuleCache.getIfPresent(this.getClass().getName() + "_getTableByName_" + tableName);
        if (obj instanceof Table) {
            return (Table) obj;
        } else {
            Table table = tableService.getOne(new LambdaQueryWrapper<Table>().eq(Table::getTableName, tableName));
            if (table == null) {
                throw new RuleExecutionException("未找到表" + tableName + "！");
            } else {
                cubeRuleCache.put(this.getClass().getName() + "_getTableByName_" + tableName, table);
            }
            return table;
        }
    }

    /**
     * 根据表id获取列名和列编码的映射关系
     *
     * @param id
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public Map<String, String> getTableColumnNameCodeMap(Integer id) {
        TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(id);
        List<TableMetaJson> tableMetaJson = tableVo.getTableMetaJson();
        Map<String, String> tableNameCodeMap = null;
        if (CollectionUtils.isNotEmpty(tableMetaJson)) {
            tableNameCodeMap = tableMetaJson.stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
        }
        return tableNameCodeMap;
    }

    /**
     * 获取子表
     *
     * @param parentTableId
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public List<TableRel> getTableRelByParentTableId(Integer parentTableId) {
        return tableRelService.list(new LambdaQueryWrapper<TableRel>().eq(TableRel::getParentTableId, parentTableId));
    }

    /**
     * @param childTableIds
     * @param tableName
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public Table getTableByIdsAndName(List<Integer> childTableIds, String tableName) {
        Table table = tableService.getOne(new LambdaQueryWrapper<Table>().in(Table::getId, childTableIds).eq(Table::getTableName, tableName));
        if (table == null) {
            throw new RuleExecutionException("未找到表" + tableName + "！");
        }
        return table;
    }

    /**
     * @param parentTableId
     * @param childTableId
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public TableRel getTableRelByParentAndChildId(Integer parentTableId, Integer childTableId) {
        return tableRelService.getOne(new LambdaQueryWrapper<TableRel>().eq(TableRel::getChildTableId, childTableId).eq(TableRel::getParentTableId, parentTableId));
    }

    @Override
    public RuleGraph getRuleGraph() {
        //查出全部的引用关系组装ruleGraph
        RuleGraph ruleGraph = ruleGraphCache.getIfPresent(RuleConstant.CACHE_PREFIX + RuleConstant.RULE_GRAPH);
        if (ruleGraph == null) {
            ruleGraph = initRuleGraphCacheInner();
        }
        return ruleGraph;
    }

    public RuleGraph initRuleGraphCacheInner() {
        // 从数据库读取规则引用关系组装ruleGraph
        RuleGraph ruleGraph = RuleGraph.create();
        List<Rule> rules = selectRuleListByType(2);
        if (CollectionUtils.isNotEmpty(rules)) {
            Map<String, Map<String, RuleVO>> ruleMap = new HashMap<>();
            List<RuleVO> ruleVOS = rules.stream().map(v -> new RuleVO().setTableId(v.getTableId()).setColumnCode(v.getColumnCode())).collect(Collectors.toList());
            List<RuleVO> refRuleVOS = rules.stream().map(v -> new RuleVO().setTableId(v.getRefTableId()).setColumnCode(v.getRefColumnCode())).collect(Collectors.toList());
            for (RuleVO ruleVO : ruleVOS) {
                Map<String, RuleVO> stringRuleMap = ruleMap.computeIfAbsent(String.valueOf(ruleVO.getTableId()), k -> new HashMap<>());
                stringRuleMap.putIfAbsent(ruleVO.getColumnCode(), ruleVO);
            }
            for (RuleVO ruleVO : refRuleVOS) {
                Map<String, RuleVO> stringRuleMap = ruleMap.computeIfAbsent(String.valueOf(ruleVO.getTableId()), k -> new HashMap<>());
                stringRuleMap.putIfAbsent(ruleVO.getColumnCode(), ruleVO);
            }
            Set<RuleVO> vertexSet = Sets.newHashSetWithExpectedSize(rules.size());
            for (Rule rule : rules) {
                RuleVO ruleVO = ruleMap.get(String.valueOf(rule.getTableId())).get(rule.getColumnCode());
                vertexSet.add(ruleVO);
            }
            for (RuleVO ruleVO : vertexSet) {
                ruleGraph.addVertex(ruleVO);
            }
            for (Rule rule : rules) {
                boolean flag = ruleGraph.addEdge(ruleMap.get(String.valueOf(rule.getTableId())).get(rule.getColumnCode()), ruleMap.get(String.valueOf(rule.getRefTableId())).get(rule.getRefColumnCode()));
                if (!flag) {
                    String ruleExpress1 = selectRuleExpressByTableIdAndColumnCode(rule.getTableId(), rule.getColumnCode());
                    String ruleExpress2 = selectRuleExpressByTableIdAndColumnCode(rule.getRefTableId(), rule.getRefColumnCode());
                    throw new RuleExecutionException("规则引用出现循环，请检查！\n" + ruleExpress1 + "\n" + ruleExpress2);
                }
            }
        }
        ruleGraphCache.put(RuleConstant.CACHE_PREFIX + RuleConstant.RULE_GRAPH, ruleGraph);
        return ruleGraph;
    }

    @PostConstruct
    public void initRuleGraphCache() {
        initRuleGraphCacheInner();
    }

    /**
     * 根据规则类型查询
     *
     * @param ruleType
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public List<Rule> selectRuleListByType(int ruleType) {
        return ruleMapper.selectList(new QueryWrapper<Rule>().select("id", "table_id", "column_code", "ref_table_id", "ref_column_code").eq("rule_type", ruleType));
    }

    /**
     * 根据表id，列名查询规则
     *
     * @param tableId
     * @param columnCode
     * @return express
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public String selectRuleExpressByTableIdAndColumnCode(Integer tableId, String columnCode) {
        Rule rule = ruleMapper.selectOne(new QueryWrapper<Rule>().select("rule_express_inner").eq("rule_type", 1).eq("table_id", tableId).eq("column_code", columnCode));
        if (rule != null) {
            return rule.getRuleExpressInner();
        }
        return null;
    }

    /**
     * 根据表id，列名查询规则
     *
     * @param tableId
     * @param columnCodes
     * @return
     */
    @Override
    @ThreadLocalCache(ThreadLocalCacheType.TTL)
    public Map<String, Map<String, Object>> selectRuleExpressByTableIdAndColumnCodes(Integer tableId, List<String> columnCodes) {
        if (tableId == null || CollectionUtils.isEmpty(columnCodes)) {
            return Collections.emptyMap();
        }
        List<Rule> ruleList = ruleMapper.selectList(new QueryWrapper<Rule>().select("id,column_code,rule_express_inner").eq("rule_type", 1).eq("table_id", tableId).in("column_code", columnCodes));
        if (CollectionUtils.isNotEmpty(ruleList)) {
            Map<String, Map<String, Object>> res = Maps.newHashMapWithExpectedSize(ruleList.size());
            for (Rule rule : ruleList) {
                Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
                map.put("id", rule.getId());
                map.put("ruleExpress", rule.getRuleExpressInner());
                res.put(rule.getColumnCode(), map);
            }
            return res;
        }
        return Collections.emptyMap();
    }

    /**
     * 查询计算规则
     *
     * @param id 计算规则主键
     * @return 计算规则
     */
    @Override
    public Rule selectCubeRuleById(Integer id) {
        return ruleMapper.selectCubeRuleById(id);
    }

    /**
     * 查询计算规则列表
     *
     * @param rule 计算规则
     * @return 计算规则
     */
    @Override
    public List<Rule> selectCubeRuleList(Rule rule) {
        return ruleMapper.selectCubeRuleList(rule);
    }

    /**
     * 新增计算规则
     *
     * @param rule 计算规则
     * @return 结果
     */
    @Override
    public int insertCubeRule(Rule rule) {
        rule.setCreateTime(DateUtils.getNowDate());
        return ruleMapper.insertCubeRule(rule);
    }

    /**
     * 修改计算规则
     *
     * @param rule 计算规则
     * @return 结果
     */
    @Override
    public int updateCubeRule(Rule rule) {
        rule.setUpdateTime(DateUtils.getNowDate());
        return ruleMapper.updateCubeRule(rule);
    }

    /**
     * 批量删除计算规则
     *
     * @param ids 需要删除的计算规则主键
     * @return 结果
     */
    @Override
    public int deleteCubeRuleByIds(Integer[] ids) {
        return ruleMapper.deleteCubeRuleByIds(ids);
    }

    /**
     * 删除计算规则信息
     *
     * @param id 计算规则主键
     * @return 结果
     */
    @Override
    public int deleteCubeRuleById(Integer id) {
        return ruleMapper.deleteCubeRuleById(id);
    }

    /**
     * 删除计算规则信息
     *
     * @param tableId    表id
     * @param columnCode 列编码
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRuleByTableIdAndColumnCode(Integer tableId, String columnCode) {
        // 清空规则列数据
        TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
        if (tableVo == null) {
            throw new ServiceException("表序号[" + tableId + "]未找到！");
        }
        MemGridUtils.deleteColumnData(tableVo.getMemTableName(), columnCode);
        return ruleMapper.delete(new LambdaQueryWrapper<Rule>().eq(Rule::getTableId, tableId).eq(Rule::getColumnCode, columnCode));
    }

    private void initLocalCacheInner(Integer tableId) {
        ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CURRENT_TABLE_ID, tableId);
        if (!Boolean.TRUE.equals(ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.INIT_LOCAL_CACHE + tableId))) {
            TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
            ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_NAME + tableId, tableVo.getTableName());// 当前表名称
            // 当前表名
            if (StringUtils.isNotEmpty(tableVo.getMemTableName())) {
                String memTableName = tableVo.getMemTableName();
//                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CURRENT_MEM_TABLE_NAME + tableId, memTableName);// 当前内存表名
                TableMetaData tableMetaData = MemGridUtils.getTableMetaData(memTableName);
                if (tableMetaData != null) {
                    // 当前表metadata
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_METADATA + tableId, tableMetaData);
                }
            }
            ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_METADATA_JSON + tableId, tableVo.getTableMetaJson());
            Map<String, String> tableNameCodeMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
            ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tableId, tableNameCodeMap);// 当前表的列名编码映射

            // 父表关联表
            TableRel parentTableRel = SpringUtils.getAopProxy(this).getTableRelByChildTableId(tableId);
            if (parentTableRel != null) {
                // 关联字段
//                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.PARENT_ASSOCIATE_FIELD + tableId, parentTableRel.getRelTableName());
//                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.PARENT_ASSOCIATE_NO + tableId, parentTableRel.getRelTableNo());
                Table table = SpringUtils.getAopProxy(this).getTableById(parentTableRel.getParentTableId());
                if (table != null) {
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.P_TABLE_NAME + tableId, table.getTableName());
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.P_MEM_TABLE_NAME + tableId, table.getMemTableName());
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.P_TABLE_ID + tableId, table.getId());
                }
            }
            // 子表关联表
            List<TableRel> childTableRels = SpringUtils.getAopProxy(this).getTableRelByParentTableId(tableId);
            if (CollectionUtils.isNotEmpty(childTableRels)) {
                ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.CHILD_ASSOCIATE_TABLES + tableId, childTableRels);
                List<Integer> childTableIds = childTableRels.stream().map(TableRel::getChildTableId).distinct().collect(Collectors.toList());
                childTableIds.forEach(childTableId -> {
                    TableVo childTableVo = SpringUtils.getAopProxy(this).getTableVoById(childTableId);
                    Map<String, String> childTableNameCodeMap = childTableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + childTableId, childTableNameCodeMap);// 当前表的列名编码映射
                });
            }
            ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.INIT_LOCAL_CACHE + tableId, Boolean.TRUE);
        }
    }

    /**
     * 初始化规则本地缓存
     *
     * @param tableId
     * @param columnCodeList
     */
    @Override
    public void initLocalCache(Integer tableId, List<String> columnCodeList) {
        long start = System.currentTimeMillis();
        initLocalCacheInner(tableId);
        TableMetaData tableMetaData = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.TABLE_METADATA + tableId);
        if (tableMetaData != null) {
            List<String> columnNames = tableMetaData.getColumnNames();
            boolean columnCodeListFlag = CollectionUtils.isNotEmpty(columnCodeList);
            Map<String, Map<String, Object>> columnExpressMap = SpringUtils.getAopProxy(this).selectRuleExpressByTableIdAndColumnCodes(tableId, columnCodeListFlag ? columnCodeList : columnNames);
            Map<Integer, String> indexColumnMap = Maps.newLinkedHashMapWithExpectedSize(columnNames.size());
            Map<Integer, String> indexExpressMap = Maps.newLinkedHashMapWithExpectedSize(columnExpressMap.size());
            Map<Integer, Long> indexRuleIdMap = Maps.newLinkedHashMapWithExpectedSize(columnExpressMap.size());
            for (int i = 0, size = columnNames.size(); i < size; i++) {
                String columnCode = columnNames.get(i);
                indexColumnMap.put(i, columnCode);
                Map columnCodeExpressMap = MapUtils.getMap(columnExpressMap, columnCode);
                if (MapUtils.isNotEmpty(columnCodeExpressMap)) {
                    indexExpressMap.put(i, MapUtils.getString(columnCodeExpressMap, "ruleExpress"));
                    indexRuleIdMap.put(i, MapUtils.getLong(columnCodeExpressMap, "id"));
                }
            }
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP, indexColumnMap);
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_EXPRESS_MAP, indexExpressMap);
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP, indexRuleIdMap);

            Map<String, Integer> reverseIndexColumnMap = indexColumnMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2));
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP, reverseIndexColumnMap);
        }
        long cost = System.currentTimeMillis() - start;
        log.info("初始化本地缓存耗时：{}ms，tableId：{}，columnCodeList：{}", cost, tableId, columnCodeList);
        if (cost > 10000) {
            log.warn("⚠️初始化本地缓存耗时：{}ms，tableId：{}，columnCodeList：{}", cost, tableId, columnCodeList);
        }
    }

    /**
     * 初始化规则本地缓存
     *
     * @param tableId
     * @param columnCode
     * @param express
     */
    @Override
    public void initLocalCache(Integer tableId, String columnCode, String express) {
        initLocalCacheInner(tableId);
        TableMetaData tableMetaData = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.TABLE_METADATA + tableId);
        if (tableMetaData != null) {
            List<String> columnNames = tableMetaData.getColumnNames();
            Map<Integer, String> indexColumnMap = Maps.newLinkedHashMapWithExpectedSize(columnNames.size());
            for (int i = 0, size = columnNames.size(); i < size; i++) {
                String columnCode1 = columnNames.get(i);
                indexColumnMap.put(i, columnCode1);
            }
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP, indexColumnMap);

            Map<String, Integer> reverseIndexColumnMap = indexColumnMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2));
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP, reverseIndexColumnMap);
        }
    }

    /**
     * 初始化规则本地缓存
     *
     * @param tableId
     */
    @Override
    public void initTableLocalCache(Integer tableId) {
        if (!Boolean.TRUE.equals(ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.INIT_TABLE_LOCAL_CACHE + tableId))) {
            TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
            if (tableVo != null && StringUtils.isNotEmpty(tableVo.getMemTableName())) {
                String memTableName = tableVo.getMemTableName();
                TableMetaData tableMetaData = MemGridUtils.getTableMetaData(memTableName);
                if (tableMetaData != null) {
                    List<String> columnNames = tableMetaData.getColumnNames();
                    Map<Integer, String> indexColumnMap = Maps.newLinkedHashMapWithExpectedSize(columnNames.size());
                    for (int i = 0, size = columnNames.size(); i < size; i++) {
                        String columnCode = columnNames.get(i);
                        indexColumnMap.put(i, columnCode);
                    }

                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP + tableId, indexColumnMap);
                    Map<String, Integer> reverseIndexColumnMap = indexColumnMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2));
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + tableId, reverseIndexColumnMap);

                    Map<String, String> tableNameCodeMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tableId, tableNameCodeMap);// 当前表的列名编码映射
                }
            }
            initLocalCacheInner(tableId);
            ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.INIT_TABLE_LOCAL_CACHE + tableId, Boolean.TRUE);
        }
    }

    /**
     * 获取内存数据
     *
     * @param table 表
     * @return
     */
    @Override
    public Object[][] getTableData(Table table) {
        if (table == null) {
            throw new RuleExecutionException("表为空！");
        }
        try {
            return MemGridUtils.getTableData(table.getMemTableName());
        } catch (ServiceException e) {
            if (Objects.equals(e.getCode(), BizConstants.TABLE_NOT_FOUND)) {
                throw new RuleExecutionException(String.format("表[%s]未载入或不存在！", table.getTableName()));
            }
            throw e;
        }
    }

    /**
     * 获取内存数据
     *
     * @param tableName 内存表名
     * @return
     */
    @Override
    public Object[][] getTableData(String tableName) {
        return MemGridUtils.getTableData(tableName);
    }

//    /**
//     * 删除计算规则
//     *
//     * @param tableId
//     * @param columnCode
//     * @return
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public int deleteRuleExpressByTableIdAndColumnCode(Integer tableId, String columnCode) {
//        SpringUtils.getAopProxy(this).deleteRuleGraphCache(tableId, columnCode);
//        stringRedisTemplate.delete(RuleConstant.CACHE_PREFIX + "GRAPH");
//        return SpringUtils.getAopProxy(this).deleteRuleByTableIdAndColumnCode(tableId, columnCode);
//    }

    /**
     * 删除计算规则缓存
     *
     * @return
     */
    @Override
    public boolean deleteRuleExpressCache() {
        ruleGraphCache.invalidate(RuleConstant.CACHE_PREFIX + RuleConstant.RULE_GRAPH);
        return true;
    }

    /**
     * 移除缓存中规则引用关系
     *
     * @param tableId
     * @param columnCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RuleGraph deleteRuleGraphCache(Integer tableId, String columnCode) {
        LambdaQueryWrapper<Rule> queryWrapper = new LambdaQueryWrapper<Rule>().eq(Rule::getTableId, tableId).eq(Rule::getRuleType, 2);
        if (StringUtils.isNotEmpty(columnCode)) {
            queryWrapper.eq(Rule::getColumnCode, columnCode);
        }
        List<Rule> ruleList = this.baseMapper.selectList(queryWrapper);
        RuleGraph ruleGraph = getRuleGraph();
        for (Rule rule1 : ruleList) {
            RuleVO origin = new RuleVO().setTableId(rule1.getTableId()).setColumnCode(rule1.getColumnCode());
            RuleVO target = new RuleVO().setTableId(rule1.getRefTableId()).setColumnCode(rule1.getRefColumnCode());
            ruleGraph.removeEdge(origin, target);
        }
        return ruleGraph;
    }

    /**
     * 将用户输入的规则处理成规则内部使用的规则
     *
     * @param express
     * @return
     */
    @Override
    public String handleRuleExpress(String express) {
        if (StringUtils.isEmpty(express)) {
            return express;
        }

        // 去除头尾空白符
        String res = express.trim();
        res = ExpressUtils.replaceRuleExpressOPCT(res);
        // 转小写
//        res = res.toLowerCase();
        return res;
    }

    /**
     * 校验规则-有无循环引用、参与四则运算的是否都是数字
     *
     * @param tableId
     * @param columnCode
     * @param express
     * @return
     */
    @Override
    public boolean validateRule(Integer tableId, String columnCode, String express) {
        return doSaveRule(tableId, columnCode, handleRuleExpress(express), false);
    }

    private boolean doSaveRule(Integer tableId, String columnCode, String ruleExpress, boolean saveFlag) {
        RuleGraph ruleGraph = this.getRuleGraph();
        RuleVO ruleVO1 = new RuleVO(tableId, columnCode);
        Map<Integer, Set<String>> expressCacheMap = new HashMap<>();

//        // 去掉日期函数中-/，避免判断为四则运算
//        Matcher strToDateMatcher = expressStrToDatePattern.matcher(ruleExpress);
//        if (strToDateMatcher.find()) {
//            String group1 = strToDateMatcher.group(1);
//            // 使用 Matcher 的 appendReplacement 和 appendTail 方法替换匹配的部分
//            // 初始化本表缓存
//            initExpressOCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 初始化父表缓存
//            initExpressPCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 初始化子表缓存
//            initExpressCCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 初始化他表缓存
//            initExpressTCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            StringBuffer sb = new StringBuffer();
//            strToDateMatcher.appendReplacement(sb, "strToDate(\"\",\"\")");
//            strToDateMatcher.appendTail(sb);
//            ruleExpress = sb.toString();
//        }
//        Matcher dateToStrMatcher = expressDateToStrPattern.matcher(ruleExpress);
//        if (dateToStrMatcher.find()) {
//            String group1 = dateToStrMatcher.group(1);
//            // 使用 Matcher 的 appendReplacement 和 appendTail 方法替换匹配的部分
//            // 初始化本表缓存
//            initExpressOCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 初始化父表缓存
//            initExpressPCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 初始化子表缓存
//            initExpressCCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 初始化他表缓存
//            initExpressTCache(tableId, columnCode, saveFlag, group1, ruleGraph, ruleVO1, false, expressCacheMap);
//            // 使用 Matcher 的 appendReplacement 和 appendTail 方法替换匹配的部分
//            StringBuffer sb = new StringBuffer();
//            dateToStrMatcher.appendReplacement(sb, "dateToStr(\"\",\"\")");
//            dateToStrMatcher.appendTail(sb);
//            ruleExpress = sb.toString();
//        }

        // 是否有四则运算
        // 去除if中的四则运算判断
        if (StringUtils.contains(ruleExpress, "if(")) {
            String ifExpress = ExpressUtils.extractFuncContent(ruleExpress, "if(");
            // 初始化本表缓存
            initExpressOCache(tableId, columnCode, saveFlag, ifExpress, ruleGraph, ruleVO1, false, expressCacheMap);
            // 初始化父表缓存
            initExpressPCache(tableId, columnCode, saveFlag, ifExpress, ruleGraph, ruleVO1, false, expressCacheMap);
            // 初始化子表缓存
            initExpressCCache(tableId, columnCode, saveFlag, ifExpress, ruleGraph, ruleVO1, false, expressCacheMap);
            // 初始化他表缓存
            initExpressTCache(tableId, columnCode, saveFlag, ifExpress, ruleGraph, ruleVO1, false, expressCacheMap);
        }
        String removeIfExpress = replaceIf(ruleExpress);
        boolean flag = false;
//      boolean flag = ARITHMETIC_OPERATOR.stream().anyMatch(removeIfExpress::contains);
        // 初始化本表缓存
        initExpressOCache(tableId, columnCode, saveFlag, removeIfExpress, ruleGraph, ruleVO1, flag, expressCacheMap);
        // 初始化父表缓存
        initExpressPCache(tableId, columnCode, saveFlag, removeIfExpress, ruleGraph, ruleVO1, flag, expressCacheMap);
        // 初始化子表缓存
        initExpressCCache(tableId, columnCode, saveFlag, removeIfExpress, ruleGraph, ruleVO1, flag, expressCacheMap);
        // 初始化他表缓存
        initExpressTCache(tableId, columnCode, saveFlag, removeIfExpress, ruleGraph, ruleVO1, flag, expressCacheMap);

        FUNC_OPERATOR.forEach(o -> validateExpressFunc(ruleExpress, o));
        return true;
    }

    private String replaceIf(String express) {
        int startIndex = express.indexOf("if(");
        while (startIndex != -1) {
            int openCount = 1;
            for (int i = startIndex + 3; i < express.length(); i++) {
                char currentChar = express.charAt(i);
                if (currentChar == '(') {
                    openCount++;
                } else if (currentChar == ')') {
                    openCount--;
                    if (openCount == 0) {
                        if (startIndex != 0) {
                            express = express.substring(0, startIndex) + express.substring(i + 1);
                        } else {
                            express = express.substring(i + 1);
                        }
                        break;
                    }
                }
            }
            if (openCount != 0) {
                break;
            }
            startIndex = express.indexOf("if(");
        }
        return express;
    }

    /**
     * 初始化他表缓存
     *
     * @param tableId
     * @param columnCode
     * @param saveFlag
     * @param express
     * @param ruleGraph
     * @param ruleVO1
     * @param checkColumnTypeFlag
     * @param expressCacheMap
     */
    private void initExpressTCache(Integer tableId, String columnCode, boolean saveFlag, String express, RuleGraph ruleGraph, RuleVO ruleVO1, boolean checkColumnTypeFlag, Map<Integer, Set<String>> expressCacheMap) {
        Map<String, Map<String, String>> tTableColumnNameCodeMap = new HashMap<>();
        Map<String, Integer> tTableNameIdMap = new HashMap<>();
        Matcher tMatcher = expressTPattern.matcher(express);
        while (tMatcher.find()) {
            String tableName = tMatcher.group(1);
            Integer tTableId = tTableNameIdMap.get(tableName);
            if (tTableId == null) {
                Table tableByName = SpringUtils.getAopProxy(this).getTableByName(tableName);
                if (tableByName == null) {
                    throw new RuleExecutionException("未找到他表：" + tableName + "！");
                }
                tTableId = tableByName.getId();
                tTableNameIdMap.put(tableName, tTableId);
            }
            TableVo tableVoById = null;
            Map<String, String> columnNameCodeMap = tTableColumnNameCodeMap.get(tableName);
            if (columnNameCodeMap == null) {
                tableVoById = SpringUtils.getAopProxy(this).getTableVoById(tTableId);
                if (tableVoById == null) {
                    throw new RuleExecutionException("未找到他表：" + tableName + "！");
                }
                columnNameCodeMap = CollectionUtils.isNotEmpty(tableVoById.getTableMetaJson()) ? tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
                tTableColumnNameCodeMap.put(tableName, columnNameCodeMap);
            }
            String columnName = tMatcher.group(2);
            Set<String> strings = expressCacheMap.computeIfAbsent(tTableId, v -> new HashSet<>());
            if (strings.contains(columnName)) {
                continue;
            }
            strings.add(columnName);
            Map tableColumnNameCodeMap = MapUtils.getMap(tTableColumnNameCodeMap, tableName);
            String tColumnCode = MapUtils.getString(tableColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(tColumnCode)) {
                throw new RuleExecutionException("他表：" + tableName + "不存在列：" + columnName);
            }
            if (checkColumnTypeFlag) {
                checkColumnType(tableVoById, tColumnCode, columnName);
            }
            RuleVO ruleVO2 = new RuleVO(tTableId, tColumnCode);
            addEdge(ruleGraph, ruleVO1, ruleVO2);
            if (saveFlag) {
                this.save(new Rule().setTableId(tableId).setColumnCode(columnCode).setRefTableId(tTableId).setRefColumnCode(tColumnCode).setRuleType(2));
            }
        }
    }


    /**
     * 初始化子表缓存
     *
     * @param tableId
     * @param columnCode
     * @param saveFlag
     * @param express
     * @param ruleGraph
     * @param ruleVO1
     * @param checkColumnTypeFlag
     * @param expressCacheMap
     */
    private void initExpressCCache(Integer tableId, String columnCode, boolean saveFlag, String express, RuleGraph ruleGraph, RuleVO ruleVO1, boolean checkColumnTypeFlag, Map<Integer, Set<String>> expressCacheMap) {
        List<TableRel> childTableRels = getTableRelByParentTableId(tableId);
        Set<Integer> childTableIds = CollectionUtils.isNotEmpty(childTableRels) ? childTableRels.stream().map(TableRel::getChildTableId).filter(Objects::nonNull).collect(Collectors.toSet()) : Collections.emptySet();
        Map<String, Map<String, String>> childTableColumnNameCodeMap = new HashMap<>();
        Map<String, Integer> childTableNameIdMap = new HashMap<>();
        Matcher cMatcher = expressCPattern.matcher(express);
        while (cMatcher.find()) {
            if (CollectionUtils.isEmpty(childTableIds)) {
                throw new RuleExecutionException("当前表没有子表！");
            }
            String tableName = cMatcher.group(1);
            Integer childTableId = childTableNameIdMap.get(tableName);
            if (childTableId == null) {
                Table tableByName = SpringUtils.getAopProxy(this).getTableByName(tableName);
                if (tableByName == null) {
                    throw new RuleExecutionException("未找到子表：" + tableName + "！");
                }
                childTableId = tableByName.getId();
                childTableNameIdMap.put(tableName, childTableId);
            }
            if (!childTableIds.contains(childTableId)) {
                throw new RuleExecutionException("表" + tableName + "不是当前表的子表！");
            }
            Map<String, String> columnNameCodeMap = childTableColumnNameCodeMap.get(tableName);
            TableVo tableVoById = null;
            if (columnNameCodeMap == null) {
                tableVoById = SpringUtils.getAopProxy(this).getTableVoById(childTableId);
                if (tableVoById == null) {
                    throw new RuleExecutionException("未找到子表：" + tableName + "！");
                }
                columnNameCodeMap = CollectionUtils.isNotEmpty(tableVoById.getTableMetaJson()) ? tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
                childTableColumnNameCodeMap.put(tableName, columnNameCodeMap);
            }
            String columnName = cMatcher.group(2);
            Set<String> strings = expressCacheMap.computeIfAbsent(childTableId, v -> new HashSet<>());
            if (strings.contains(columnName)) {
                continue;
            }
            strings.add(columnName);
            Map tableColumnNameCodeMap = MapUtils.getMap(childTableColumnNameCodeMap, tableName);
            String childColumnCode = MapUtils.getString(tableColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(childColumnCode)) {
                throw new RuleExecutionException("子表：" + tableName + "不存在列：" + columnName);
            }
            if (checkColumnTypeFlag) {
                checkColumnType(tableVoById, childColumnCode, columnName);
            }
            RuleVO ruleVO2 = new RuleVO(childTableId, childColumnCode);
            addEdge(ruleGraph, ruleVO1, ruleVO2);
            if (saveFlag) {
                this.save(new Rule().setTableId(tableId).setColumnCode(columnCode).setRefTableId(childTableId).setRefColumnCode(childColumnCode).setRuleType(2));
            }
        }
    }

    /**
     * 初始化父表缓存
     *
     * @param tableId
     * @param columnCode
     * @param saveFlag
     * @param express
     * @param ruleGraph
     * @param ruleVO1
     * @param checkColumnTypeFlag
     * @param expressCacheMap
     */
    private void initExpressPCache(Integer tableId, String columnCode, boolean saveFlag, String express, RuleGraph ruleGraph, RuleVO ruleVO1, boolean checkColumnTypeFlag, Map<Integer, Set<String>> expressCacheMap) {
        // 查父表
        // 父表关联表
        Integer parentTableId = null;
        TableVo parentTableVo = null;
        Map<String, String> parentColumnNameCodeMap = Collections.emptyMap();
        TableRel parentTableRel = getTableRelByChildTableId(tableId);
        if (parentTableRel != null) {
            parentTableId = parentTableRel.getParentTableId();
            if (parentTableId != null) {
                parentTableVo = SpringUtils.getAopProxy(this).getTableVoById(parentTableId);
                if (parentTableVo != null && CollectionUtils.isNotEmpty(parentTableVo.getTableMetaJson())) {
                    parentColumnNameCodeMap = parentTableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
                }
            }
        }
        Matcher pMatcher = expressPPattern.matcher(express);
        while (pMatcher.find()) {
            if (parentTableId == null || parentTableVo == null) {
                throw new RuleExecutionException("未找到父表！");
            }
            String columnName = pMatcher.group(1);
            Set<String> strings = expressCacheMap.computeIfAbsent(parentTableId, v -> new HashSet<>());
            if (strings.contains(columnName)) {
                continue;
            }
            strings.add(columnName);
            String parentColumnCode = MapUtils.getString(parentColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(parentColumnCode)) {
                throw new RuleExecutionException("父表不存在列：" + columnName);
            }
            if (checkColumnTypeFlag) {
                checkColumnType(parentTableVo, parentColumnCode, columnName);
            }

            RuleVO ruleVO2 = new RuleVO(parentTableId, parentColumnCode);
            addEdge(ruleGraph, ruleVO1, ruleVO2);
            if (saveFlag) {
                this.save(new Rule().setTableId(tableId).setColumnCode(columnCode).setRefTableId(parentTableId).setRefColumnCode(parentColumnCode).setRuleType(2));
            }
        }
    }

    /**
     * 初始化本表缓存
     *
     * @param tableId
     * @param columnCode
     * @param saveFlag
     * @param express
     * @param ruleGraph
     * @param ruleVO1
     * @param checkColumnTypeFlag
     * @param expressCacheMap
     */
    private void initExpressOCache(Integer tableId, String columnCode, boolean saveFlag, String express, RuleGraph ruleGraph, RuleVO ruleVO1, boolean checkColumnTypeFlag, Map<Integer, Set<String>> expressCacheMap) {
        Matcher oMatcher = expressOPattern.matcher(express);
        TableVo currentTableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
        Map<String, String> currentColumnNameCodeMap = CollectionUtils.isNotEmpty(currentTableVo.getTableMetaJson()) ? currentTableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
        while (oMatcher.find()) {
            String columnName = oMatcher.group(1);
            Set<String> strings = expressCacheMap.computeIfAbsent(tableId, v -> new HashSet<>());
            if (strings.contains(columnName)) {
                continue;
            }
            strings.add(columnName);
            String columnCode2 = MapUtils.getString(currentColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(columnCode2)) {
                throw new RuleExecutionException("当前表不存在列：" + columnName);
            }
            if (checkColumnTypeFlag) {
                checkColumnType(currentTableVo, columnCode2, columnName);
            }
            RuleVO ruleVO2 = new RuleVO(tableId, columnCode2);
            addEdge(ruleGraph, ruleVO1, ruleVO2);
            if (saveFlag) {
                this.save(new Rule().setTableId(tableId).setColumnCode(columnCode).setRefTableId(tableId).setRefColumnCode(columnCode2).setRuleType(2));
            }
        }
    }

    /**
     * 校验函数表达式
     *
     * @param express
     * @param func
     */
    private void validateExpressFunc(String express, String func) {
        String ruleFunc = func + "(";
        if (StringUtils.contains(express, ruleFunc)) {
            String funcExpress = ExpressUtils.extractFuncContent(express, ruleFunc);
            if (StringUtils.isNotEmpty(funcExpress)) {
                String tableName = "";
                Matcher expressCountTMatcher = expressTPattern.matcher(funcExpress);
                while (expressCountTMatcher.find()) {
                    if (StringUtils.isEmpty(tableName)) {
                        tableName = expressCountTMatcher.group(1);
                    } else {
                        if (!StringUtils.equals(tableName, expressCountTMatcher.group(1))) {
                            throw new RuleExecutionException(ruleFunc + "函数中他表需要保持一致");
                        }
                    }
                }
            }
        }
    }


    /**
     * 保存规则引用关系
     *
     * @param tableId
     * @param columnCode
     * @param express
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRuleRef(Integer tableId, String columnCode, String express) {
        return doSaveRule(tableId, columnCode, express, true);
    }

    private static void addEdge(RuleGraph ruleGraph, RuleVO ruleVO1, RuleVO ruleVO2) {
        if (!ruleGraph.hasEdge(ruleVO1, ruleVO2)) {
            ruleGraph.addVertex(ruleVO1);
            ruleGraph.addVertex(ruleVO2);
            boolean addEdgeFlag = ruleGraph.addEdge(ruleVO1, ruleVO2);
            if (!addEdgeFlag) {
                throw new RuleExecutionException("规则出现循环引用，请检查！");
            }
        }
    }

    private static void checkColumnType(TableVo tableVo, String fieldCode, String columnName) {
        String columnType = StringUtils.EMPTY;
        for (TableMetaJson curJson : tableVo.getTableMetaJson()) {
            if (!StringUtils.equals(fieldCode, curJson.getCode())) {
                continue;
            }
            // 为兼容数据格式 优先赋值存储格式
            if (curJson.getDataFormat() != null) {
                columnType = curJson.getDataFormat().getStorageType();
            } else if (StringUtils.isNotEmpty(curJson.getNewColumnType())) {
                columnType = curJson.getNewColumnType();
            } else {
                columnType = "VARCHAR";
            }
            break;
        }

        Class cl = MemGridUtils.getColumnType(columnType);
        if (!Integer.class.equals(cl) && !Double.class.equals(cl)) {
            throw new RuleExecutionException("表：" + tableVo.getTableName() + "，列：" + columnName + "不是数字类型！");
        }
    }

    @Override
    public RuleQuickInputVO getQuickInput(Integer tableId) {
        return getQuickInput(tableId, "grid", null);
    }

    /**
     * @param tableId
     * @return
     */
    @Override
    public RuleQuickInputVO getQuickInput(Integer tableId, String type, Long dimTableId) {
        RuleQuickInputVO quickInputVO = new RuleQuickInputVO();
        List<RuleFunc> funcList = ruleFuncService.getAll();
        // 二维
        if (StringUtils.equals(type, "grid")) {
            Set<Integer> opcIdSet = new HashSet<>();
            // 本表字段
            TableVo o = getTableVoById(tableId);
            if (o != null) {
                List<TableMetaJson> oTableMetaJson = o.getTableMetaJson();
                if (CollectionUtils.isNotEmpty(oTableMetaJson)) {
                    List<RuleRefVO> oRefListDto = new ArrayList<>(oTableMetaJson.size());
                    for (TableMetaJson tableMetaJson : oTableMetaJson) {
                        RuleRefVO ruleRefVO = new RuleRefVO();
                        ruleRefVO.setTableId(o.getId());
                        ruleRefVO.setTableParentId(o.getParentId());
                        ruleRefVO.setTableAncestors(o.getAncestors());
                        ruleRefVO.setName(tableMetaJson.getName());
                        ruleRefVO.setDesc("本表引用");
                        ruleRefVO.setValue("o[\"" + tableMetaJson.getName() + "\"]");
                        oRefListDto.add(ruleRefVO);
                    }
                    quickInputVO.setO(oRefListDto);
                }
                opcIdSet.add(tableId);
            }

            // 查父表
            // 父表关联表
            TableRel parentTableRel = getTableRelByChildTableId(tableId);
            if (parentTableRel != null) {
                Integer parentTableId = parentTableRel.getParentTableId();
                // 关联字段
                TableVo p = getTableVoById(parentTableId);
                if (p != null) {
                    List<TableMetaJson> pTableMetaJson = p.getTableMetaJson();
                    if (CollectionUtils.isNotEmpty(pTableMetaJson)) {
                        List<RuleRefVO> pRefListDto = new ArrayList<>(pTableMetaJson.size());
                        for (TableMetaJson tableMetaJson : pTableMetaJson) {
                            RuleRefVO ruleRefVO = new RuleRefVO();
                            ruleRefVO.setTableId(p.getId());
                            ruleRefVO.setTableParentId(p.getParentId());
                            ruleRefVO.setTableAncestors(p.getAncestors());
                            ruleRefVO.setName(tableMetaJson.getName());
                            ruleRefVO.setDesc("父表引用");
                            ruleRefVO.setValue("p[\"" + tableMetaJson.getName() + "\"]");
                            pRefListDto.add(ruleRefVO);
                        }
                        quickInputVO.setP(pRefListDto);
                    }
                    opcIdSet.add(parentTableId);
                }
            }
            // 查子表
            // 子表关联表
            List<TableRel> childTableRels = getTableRelByParentTableId(tableId);
            if (CollectionUtils.isNotEmpty(childTableRels)) {
                List<TableVo> tableVoList = getTableVoByIds(childTableRels.stream().map(TableRel::getChildTableId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(tableVoList)) {
                    int size = tableVoList.size();
                    List<RuleRefVO> c = new ArrayList<>(size);
                    for (int i = 0; i < size; i++) {
                        TableVo tableVo = tableVoList.get(i);
                        if (tableVo == null) {
                            continue;
                        }
                        RuleRefVO ruleRefVO = new RuleRefVO();
                        ruleRefVO.setTableId(tableVo.getId());
                        ruleRefVO.setTableParentId(tableVo.getParentId());
                        ruleRefVO.setTableAncestors(tableVo.getAncestors());
                        ruleRefVO.setName(tableVo.getTableName());
                        ruleRefVO.setValue(String.valueOf(i));
                        ruleRefVO.setDesc("子表");
                        List<TableMetaJson> cTableMetaJson = tableVo.getTableMetaJson();
                        if (CollectionUtils.isNotEmpty(cTableMetaJson)) {
                            List<RuleRefVO> cRefList = new ArrayList<>(cTableMetaJson.size());
                            for (TableMetaJson tableMetaJson : cTableMetaJson) {
                                RuleRefVO cRuleRefVO = new RuleRefVO();
                                cRuleRefVO.setTableId(tableVo.getId());
                                cRuleRefVO.setTableParentId(tableVo.getParentId());
                                cRuleRefVO.setTableAncestors(tableVo.getAncestors());
                                cRuleRefVO.setName(tableMetaJson.getName());
                                cRuleRefVO.setDesc("子表引用");
                                cRuleRefVO.setValue("c[\"" + tableVo.getTableName() + "\",\"" + tableMetaJson.getName() + "\"]");
                                cRefList.add(cRuleRefVO);
                            }
                            ruleRefVO.setChildren(cRefList);
                        }
                        c.add(ruleRefVO);
                    }
                    opcIdSet.addAll(tableVoList.stream().map(TableVo::getId).filter(Objects::nonNull).collect(Collectors.toSet()));
                    quickInputVO.setC(c);
                }

            }

            // 查他表，修改为文件夹形式
            TableListQuery query = new TableListQuery();
//            qu4ery.setFilterChildTable(Boolean.TRUE);
            List<Table> list = tableService.list(query);
            if (CollectionUtils.isNotEmpty(list)) {
                List<Table> filterOPCList = list.stream().filter(v -> !opcIdSet.contains(v.getId())).collect(Collectors.toList());
                int size = filterOPCList.size();
                List<RuleRefVO> t = new ArrayList<>(size);
                for (int i = 0; i < size; i++) {
                    Table table = filterOPCList.get(i);
                    if (table == null) {
                        continue;
                    }
                    RuleRefVO ruleRefVO = new RuleRefVO();
                    ruleRefVO.setTableId(table.getId());
                    ruleRefVO.setTableParentId(table.getParentId());
                    ruleRefVO.setTableAncestors(table.getAncestors());
                    ruleRefVO.setName(table.getTableName());
                    ruleRefVO.setValue(String.valueOf(i));
                    ruleRefVO.setDesc("他表");
                    String tTableMeta = table.getTableMeta();
                    if (StringUtils.isNotEmpty(tTableMeta)) {
                        List<TableMetaJson> tTableMetaJson = JSON.parseArray(tTableMeta, TableMetaJson.class);
                        if (CollectionUtils.isNotEmpty(tTableMetaJson)) {
                            List<RuleRefVO> tRefList = new ArrayList<>(tTableMetaJson.size());
                            for (TableMetaJson tableMetaJson : tTableMetaJson) {
                                RuleRefVO tRuleRefVO = new RuleRefVO();
                                tRuleRefVO.setTableId(table.getId());
                                tRuleRefVO.setTableParentId(table.getParentId());
                                tRuleRefVO.setTableAncestors(table.getAncestors());
                                tRuleRefVO.setName(tableMetaJson.getName());
                                tRuleRefVO.setDesc("他表引用");
                                tRuleRefVO.setValue("t[\"" + table.getTableName() + "\",\"" + tableMetaJson.getName() + "\"]");
                                tRefList.add(tRuleRefVO);
                            }
                            ruleRefVO.setChildren(tRefList);
                        }
                    }
                    t.add(ruleRefVO);
                }
                quickInputVO.setT(t);
            }
        } else if (StringUtils.equals(type, "cube")) {
            funcList = funcList.stream().filter(v -> StringUtils.contains(v.getFuncScope(), "2")).collect(Collectors.toList());
            // 本表字段
            DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(Math.toIntExact(dimTableId));
            if (dimTableInfoVo != null) {
                List<RuleRefVO> oRefListDto = new ArrayList<>();
                List<DimDirectory> dimList = dimTableInfoVo.getDimList();
                if (CollectionUtils.isNotEmpty(dimList)) {
                    oRefListDto.addAll(dimList
                            .stream()
                            .filter(v -> !StringUtils.equals(v.getDimDirectoryType(), "GROUP"))
                            .map(dimDirectory -> {
                                RuleRefVO ruleRefVO = new RuleRefVO();
                                ruleRefVO.setTableId(dimTableInfoVo.getId());
                                ruleRefVO.setTableParentId(dimTableInfoVo.getParentId());
                                ruleRefVO.setTableAncestors(dimTableInfoVo.getAncestors());
                                ruleRefVO.setName(dimDirectory.getDimDirectoryName());
                                ruleRefVO.setDesc("本表引用");
                                ruleRefVO.setValue("o[\"" + dimDirectory.getDimDirectoryName() + "\"]");
                                return ruleRefVO;
                            })
                            .collect(Collectors.toList()));
                }
                List<Ind> indList = dimTableInfoVo.getIndList();
                if (CollectionUtils.isNotEmpty(indList)) {
                    oRefListDto.addAll(indList
                            .stream()
                            .filter(v -> !StringUtils.equals(v.getIndType(), "GROUP"))
                            .map(ind -> {
                                RuleRefVO ruleRefVO = new RuleRefVO();
                                ruleRefVO.setTableId(dimTableInfoVo.getId());
                                ruleRefVO.setTableParentId(dimTableInfoVo.getParentId());
                                ruleRefVO.setTableAncestors(dimTableInfoVo.getAncestors());
                                ruleRefVO.setName(ind.getIndName());
                                ruleRefVO.setDesc("本表引用");
                                ruleRefVO.setValue("o[\"" + ind.getIndName() + "\"]");
                                return ruleRefVO;
                            })
                            .collect(Collectors.toList()));
                }
                quickInputVO.setO(oRefListDto);
            }
            // 多维他表
            DimTableListQuery dimTableListQuery = new DimTableListQuery();
            List<DimTable> list = dimTableService.list(dimTableListQuery);
            if (CollectionUtils.isNotEmpty(list)) {
                List<RuleRefVO> ruleRefVOS = list.stream()
                        .filter(v -> !StringUtils.equals(v.getType(), "GROUP"))
                        .map(DimTable::getId)
                        .filter(id -> !Objects.equals(id, Math.toIntExact(dimTableId)))
                        .map(dimTableService::getByIdExt)
                        .map(v -> {
                            RuleRefVO ruleRefVO = new RuleRefVO();
                            ruleRefVO.setTableId(v.getId());
                            ruleRefVO.setTableParentId(v.getParentId());
                            ruleRefVO.setTableAncestors(v.getAncestors());
                            ruleRefVO.setName(v.getTableName());
                            ruleRefVO.setDesc("他表");
                            List<RuleRefVO> tRefList = new ArrayList<>();
                            List<DimDirectory> dimList = v.getDimList();
                            if (CollectionUtils.isNotEmpty(dimList)) {
                                for (DimDirectory dimDirectory : dimList) {
                                    RuleRefVO tRuleRefVO = new RuleRefVO();
                                    tRuleRefVO.setTableId(v.getId());
                                    tRuleRefVO.setTableParentId(v.getParentId());
                                    tRuleRefVO.setTableAncestors(v.getAncestors());
                                    tRuleRefVO.setName(dimDirectory.getDimDirectoryName());
                                    tRuleRefVO.setDesc("他表引用");
                                    tRuleRefVO.setValue("t[\"cube\",\"" + v.getTableName() + "\",\"" + dimDirectory.getDimDirectoryName() + "\"]");
                                    tRefList.add(tRuleRefVO);
                                }
                                ruleRefVO.setChildren(tRefList);
                            }
                            List<Ind> indList = v.getIndList();
                            if (CollectionUtils.isNotEmpty(indList)) {
                                for (Ind ind : indList) {
                                    RuleRefVO tRuleRefVO = new RuleRefVO();
                                    tRuleRefVO.setTableId(v.getId());
                                    tRuleRefVO.setTableParentId(v.getParentId());
                                    tRuleRefVO.setTableAncestors(v.getAncestors());
                                    tRuleRefVO.setName(ind.getIndName());
                                    tRuleRefVO.setDesc("他表引用");
                                    tRuleRefVO.setValue("t[\"cube\",\"" + v.getTableName() + "\",\"" + ind.getIndName() + "\"]");
                                    tRefList.add(tRuleRefVO);
                                }
                                ruleRefVO.setChildren(tRefList);
                            }
                            return ruleRefVO;
                        }).collect(Collectors.toList());
                quickInputVO.setT(ruleRefVOS);
            }

            // 查二维他表，修改为文件夹形式
            TableListQuery query = new TableListQuery();
//            query.setFilterChildTable(Boolean.TRUE);
            List<Table> tableList = tableService.list(query);
            if (CollectionUtils.isNotEmpty(tableList)) {
                List<RuleRefVO> ruleRefVOS = tableList.stream()
                        .filter(table -> !StringUtils.equals(table.getChildFlag(), "Y"))
                        .map(table -> {
                            RuleRefVO ruleRefVO = new RuleRefVO();
                            ruleRefVO.setTableId(table.getId());
                            ruleRefVO.setTableParentId(table.getParentId());
                            ruleRefVO.setTableAncestors(table.getAncestors());
                            ruleRefVO.setName(table.getTableName());
                            ruleRefVO.setValue("");
                            ruleRefVO.setDesc("他表");
                            String tTableMeta = table.getTableMeta();
                            if (StringUtils.isNotEmpty(tTableMeta)) {
                                List<TableMetaJson> tTableMetaJson = JSON.parseArray(tTableMeta, TableMetaJson.class);
                                if (CollectionUtils.isNotEmpty(tTableMetaJson)) {
                                    List<RuleRefVO> tRefList = new ArrayList<>(tTableMetaJson.size());
                                    for (TableMetaJson tableMetaJson : tTableMetaJson) {
                                        RuleRefVO tRuleRefVO = new RuleRefVO();
                                        tRuleRefVO.setTableId(table.getId());
                                        tRuleRefVO.setTableParentId(table.getParentId());
                                        tRuleRefVO.setTableAncestors(table.getAncestors());
                                        tRuleRefVO.setName(tableMetaJson.getName());
                                        tRuleRefVO.setDesc("他表引用");
                                        tRuleRefVO.setValue("t[\"grid\",\"" + table.getTableName() + "\",\"" + tableMetaJson.getName() + "\"]");
                                        tRefList.add(tRuleRefVO);
                                    }
                                    ruleRefVO.setChildren(tRefList);
                                }
                            }
                            return ruleRefVO;
                        }).collect(Collectors.toList());
                quickInputVO.setT2(ruleRefVOS);
            }
        }
        Map<String, List<RuleFunc>> collect = funcList.stream().collect(Collectors.groupingBy(RuleFunc::getGroupName));
        List<RuleFunc> funcs = new ArrayList<>(collect.size());
        int funcVal = 0;
        for (Map.Entry<String, List<RuleFunc>> entry : collect.entrySet()) {
            RuleFunc ruleFunc = new RuleFunc();
            ruleFunc.setFuncName(entry.getKey());
            ruleFunc.setFuncVal(String.valueOf(funcVal++));
            ruleFunc.setGroupSeq(entry.getValue().get(0).getGroupSeq());
            ruleFunc.setRuleFuncList(entry.getValue().stream().sorted(Comparator.comparing(RuleFunc::getFuncSeq)).collect(Collectors.toList()));
            funcs.add(ruleFunc);
        }
        funcs = funcs.stream().sorted(Comparator.comparing(RuleFunc::getGroupSeq)).collect(Collectors.toList());
        quickInputVO.setFunc(funcs);
        String ifConfig = sysConfigService.selectConfigByKey("sys.rule.express.if");
        List<RuleRefVO> expressIf = new ArrayList<>(1);
        RuleRefVO ifRefDto = new RuleRefVO();
        ifRefDto.setName("if");
        ifRefDto.setValue(ifConfig);
        ifRefDto.setDesc("if表达式");
        expressIf.add(ifRefDto);
        quickInputVO.setExpressIf(expressIf);
        return quickInputVO;
    }

    @Override
    public List<RuleFunc> aggregationFunList() {
        List<RuleFunc> list = ruleFuncService.list(new QueryWrapper<RuleFunc>().lambda().eq(RuleFunc::getFuncGroup, 1).orderByAsc(RuleFunc::getFuncSeq));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<RuleFunc> res = new ArrayList<>(list.size() + 1);
        RuleFunc ruleFunc = new RuleFunc();
        ruleFunc.setFuncName(BizConstants.NOT_SUMMARIZE);
        ruleFunc.setAlias("NotSummarize");
        ruleFunc.setFuncVal("NotSummarize");
        ruleFunc.setFuncSeq(0);
        res.add(ruleFunc);
        res.addAll(list);
        return res;
    }

    @Override
    public List<JobRuleVO> getRulesByJobId(Integer jobId) {
        List<CubeJobDetail> jobDetails = jobDetailService.getByJobId(jobId);
        if (CollectionUtils.isEmpty(jobDetails)) {
            return Collections.emptyList();
        }
        Map<Integer, String> tableIdNameMap = jobDetails.stream().collect(Collectors.toMap(CubeJobDetail::getTableId, CubeJobDetail::getTableName, (v1, v2) -> v2));
        Map<Integer, Map<Integer, List<String>>> executeColumnCodeMap = ExpressUtils.collectRuleRef(new ArrayList<>(tableIdNameMap.keySet()));
        executeColumnCodeMap.remove(0);
        if (MapUtils.isEmpty(executeColumnCodeMap)) {
            return Collections.emptyList();
        }
        List<JobRuleVO> list = new ArrayList<>();
        int sequence = 1;
        int batchCount = 1;
        Map<Integer, Map<String, String>> tableMetaJsonMap = Maps.newHashMapWithExpectedSize(jobDetails.size());
        for (Map.Entry<Integer, Map<Integer, List<String>>> entry : executeColumnCodeMap.entrySet()) {
            if (MapUtils.isEmpty(entry.getValue())) {
                continue;
            }
            for (Map.Entry<Integer, List<String>> entry1 : entry.getValue().entrySet()) {
                if (CollectionUtils.isEmpty(entry1.getValue())) {
                    continue;
                }
                Integer tableId = entry1.getKey();
                TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
                Map<String, String> collect = tableMetaJsonMap.computeIfAbsent(tableId, k -> {
                    List<TableMetaJson> tableMetaJson = tableVo.getTableMetaJson();
                    return tableMetaJson.stream().collect(Collectors.toMap(TableMetaJson::getCode, TableMetaJson::getName, (v1, v2) -> v2));
                });
                tableIdNameMap.computeIfAbsent(tableId, k -> tableVo.getTableName());
                List<String> columnNames = entry1.getValue().stream().map(v -> MapUtils.getString(collect, v)).collect(Collectors.toList());
                JobRuleVO ruleVO = new JobRuleVO();
                ruleVO.setTableName(MapUtils.getString(tableIdNameMap, tableId));
                ruleVO.setColumnName(String.join(",", columnNames));
                ruleVO.setSequence(sequence++);
                ruleVO.setBatchCount(batchCount);
                list.add(ruleVO);
            }
            batchCount++;
            sequence = 1;
        }

        return list;
    }

    @Override
    public List<FunctionSuggestionVo> getFunctionList(Integer tableId) {
        List<FunctionSuggestionVo> functions = new ArrayList<>();
        fetchOFCTFunctions((functions));
        RuleQuickInputVO ruleQuickInputVo = this.getQuickInput(tableId);
        if (Objects.nonNull(ruleQuickInputVo)) {
            List<RuleFunc> funcs = ruleQuickInputVo.getFunc();
            if (CollectionUtils.isNotEmpty(funcs)) {
                for (RuleFunc group : funcs) {
                    for (RuleFunc func : group.getRuleFuncList()) {
                        FunctionSuggestionVo functionVo = FunctionSuggestionVo.builder()
                                .label(func.getAlias())
                                .kind("monaco.languages.CompletionItemKind.Function")
                                .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                                .insertText(func.getFuncVal())
                                .detail(func.getFuncDesc())
                                .build();
                        functions.add(functionVo);
                    }
                }
            }
            List<RuleRefVO> expressIfs = ruleQuickInputVo.getExpressIf();
            if (CollectionUtils.isNotEmpty(expressIfs)) {
                for (RuleRefVO ruleRef : expressIfs) {
                    FunctionSuggestionVo functionVo = FunctionSuggestionVo.builder()
                            .label(ruleRef.getName())
                            .kind("monaco.languages.CompletionItemKind.Function")
                            .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                            .insertText(ruleRef.getValue())
                            .detail(ruleRef.getDesc())
                            .build();
                    functions.add(functionVo);
                }
            }
            List<RuleRefVO> owns = ruleQuickInputVo.getO();
            if (CollectionUtils.isNotEmpty(owns)) {
                for (RuleRefVO ruleRef : owns) {
                    FunctionSuggestionVo functionVo = FunctionSuggestionVo.builder()
                            .label(ruleRef.getName())
                            .kind("monaco.languages.CompletionItemKind.TypeParameter")
                            .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                            .insertText("\"" + ruleRef.getName() + "\"")
                            .detail(ruleRef.getDesc() + "-" + ruleRef.getName())
                            .build();
                    functions.add(functionVo);
                }
            }
            List<RuleRefVO> others = ruleQuickInputVo.getT();
            if (CollectionUtils.isNotEmpty(others)) {
                for (RuleRefVO ruleRef : others) {
                    FunctionSuggestionVo functionVo = FunctionSuggestionVo.builder()
                            .label(ruleRef.getName())
                            .kind("monaco.languages.CompletionItemKind.TypeParameter")
                            .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                            .insertText("\"" + ruleRef.getName() + "\"")
                            .detail(ruleRef.getDesc() + "-" + ruleRef.getName())
                            .build();
                    functions.add(functionVo);
                }
            }

        }
        return functions;
    }

    @Override
    public boolean validateVisibleCondition(Integer tableId, String dataId, String visibleCondition) {
        if (tableId == null || StringUtils.isBlank(dataId) || StringUtils.isBlank(visibleCondition)) {
            return false;
        }
        initLocalCache(tableId, null);
        Object[][] tableData = this.getTableData(SpringUtils.getAopProxy(this).getTableById(tableId));
        if (ArrayUtils.isEmpty(tableData)) {
            return false;
        }
        Map<String, Object[]> tableDataMap = Arrays.stream(tableData).collect(Collectors.toMap(v -> String.valueOf(v[0]), Function.identity()));
        String[] ids = dataId.split(",");
        for (String id : ids) {
            Object[] row = MapUtils.getObject(tableDataMap, id);
            if (ArrayUtils.isEmpty(row)) {
                continue;
            }
            Object obj = getRuleExpressResult(tableId, row, visibleCondition);
            if (!(obj instanceof Boolean) || !Boolean.TRUE.equals(obj)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 执行流程计算规则
     *
     * @param tableId
     * @param dataId
     * @param ruleExpress
     * @return
     */
    @Override
    public void executeWFComputeRule(Integer tableId, String dataId, String ruleExpress) {
        if (tableId == null || StringUtils.isBlank(dataId) || StringUtils.isBlank(ruleExpress)) {
            return;
        }
        initLocalCache(tableId, null);
        Object[][] tableData = this.getTableData(SpringUtils.getAopProxy(this).getTableById(tableId));
        if (ArrayUtils.isEmpty(tableData)) {
            return;
        }
        Map<String, Object[]> tableDataMap = Arrays.stream(tableData).collect(Collectors.toMap(v -> String.valueOf(v[0]), Function.identity()));
        String[] ids = dataId.split(",");
        for (String id : ids) {
            Object[] row = MapUtils.getObject(tableDataMap, id);
            if (ArrayUtils.isEmpty(row)) {
                continue;
            }
            Map<String, Object> context = Maps.newHashMapWithExpectedSize(2);
            context.put(RuleOperatorEnum.O.name(), row);// 当前表字段及值
            context.put(RuleConstant.RULE_EXPRESS, ruleExpress);// 规则
            expressUtils.executeWithoutException(ruleExpress, context);
        }
    }

    @Override
    public String generateSubTableKey(String tableName, Object[] rowData) {
        Integer cTableId = SpringUtils.getAopProxy(this).getTableByName(tableName).getId();
        TableVo tableVo = SpringUtils.getAopProxy(this).getTableVoById(cTableId);
        Integer tableLevel = tableVo.getTableLevel();
        List<String> keyList = new ArrayList<>(tableLevel);
        for (int i = 0; i < tableLevel; i++) {
            keyList.add(String.valueOf(rowData[i]));
        }
        return String.join("_", keyList);
    }

    @Override
    public ValidateRuleVO validateRule2(Integer tableId, String columnCode, String express) {
        boolean flag = ARITHMETIC_OPERATOR.stream().anyMatch(express::contains);
        ValidateRuleVO validateRuleVO = new ValidateRuleVO();
        if (flag) {
            // 校验表列的类型
            try {
                // o
                checkOColumnType(tableId, express);
                // p
                checkPColumnType(tableId, express);
                // c
                checkCColumnType(tableId, express);
                // t
                checkTColumnType(express);
            } catch (RuleExecutionException e) {
                log.error("规则校验失败：", e);
                validateRuleVO.setType("warn");
                validateRuleVO.setMsg("请注意：参与四则运算的列必须是数字类型，如果列有空值，请先用函数做“空值赋0”");
                return validateRuleVO;
            }
        }
        try {
            doSaveRule(tableId, columnCode, handleRuleExpress(express), false);
        } catch (RuleExecutionException e) {
            log.error("规则校验失败：", e);
            validateRuleVO.setType("error");
            validateRuleVO.setMsg("规则校验失败：" + ExceptionUtil.getLocalizedMessage(e));
            return validateRuleVO;
        }
        validateRuleVO.setType("success");
        validateRuleVO.setMsg("校验成功！");
        return validateRuleVO;
    }

    private void checkOColumnType(Integer tableId, String ruleExpress) {
        Matcher oMatcher = expressOPattern.matcher(ruleExpress);
        TableVo currentTableVo = SpringUtils.getAopProxy(this).getTableVoById(tableId);
        Map<String, String> currentColumnNameCodeMap = CollectionUtils.isNotEmpty(currentTableVo.getTableMetaJson()) ? currentTableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
        while (oMatcher.find()) {
            String columnName = oMatcher.group(1);
            String columnCode2 = MapUtils.getString(currentColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(columnCode2)) {
                throw new RuleExecutionException("当前表不存在列：" + columnName);
            }
            checkColumnType(currentTableVo, columnCode2, columnName);
        }
    }

    private void checkPColumnType(Integer tableId, String express) {
        Integer parentTableId = null;
        TableVo parentTableVo = null;
        Map<String, String> parentColumnNameCodeMap = Collections.emptyMap();
        TableRel parentTableRel = getTableRelByChildTableId(tableId);
        if (parentTableRel != null) {
            parentTableId = parentTableRel.getParentTableId();
            if (parentTableId != null) {
                parentTableVo = SpringUtils.getAopProxy(this).getTableVoById(parentTableId);
                if (parentTableVo != null && CollectionUtils.isNotEmpty(parentTableVo.getTableMetaJson())) {
                    parentColumnNameCodeMap = parentTableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
                }
            }
        }
        Matcher pMatcher = expressPPattern.matcher(express);
        while (pMatcher.find()) {
            if (parentTableId == null || parentTableVo == null) {
                throw new RuleExecutionException("未找到父表！");
            }
            String columnName = pMatcher.group(1);
            String parentColumnCode = MapUtils.getString(parentColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(parentColumnCode)) {
                throw new RuleExecutionException("父表不存在列：" + columnName);
            }
            checkColumnType(parentTableVo, parentColumnCode, columnName);
        }
    }

    private void checkCColumnType(Integer tableId, String express) {
        List<TableRel> childTableRels = getTableRelByParentTableId(tableId);
        Set<Integer> childTableIds = CollectionUtils.isNotEmpty(childTableRels) ? childTableRels.stream().map(TableRel::getChildTableId).filter(Objects::nonNull).collect(Collectors.toSet()) : Collections.emptySet();
        Map<String, Map<String, String>> childTableColumnNameCodeMap = new HashMap<>();
        Map<String, Integer> childTableNameIdMap = new HashMap<>();
        Matcher cMatcher = expressCPattern.matcher(express);
        while (cMatcher.find()) {
            if (CollectionUtils.isEmpty(childTableIds)) {
                throw new RuleExecutionException("当前表没有子表！");
            }
            String tableName = cMatcher.group(1);
            Integer childTableId = childTableNameIdMap.get(tableName);
            if (childTableId == null) {
                Table tableByName = SpringUtils.getAopProxy(this).getTableByName(tableName);
                if (tableByName == null) {
                    throw new RuleExecutionException("未找到子表：" + tableName + "！");
                }
                childTableId = tableByName.getId();
                childTableNameIdMap.put(tableName, childTableId);
            }
            if (!childTableIds.contains(childTableId)) {
                throw new RuleExecutionException("表" + tableName + "不是当前表的子表！");
            }
            Map<String, String> columnNameCodeMap = childTableColumnNameCodeMap.get(tableName);
            TableVo tableVoById = null;
            if (columnNameCodeMap == null) {
                tableVoById = SpringUtils.getAopProxy(this).getTableVoById(childTableId);
                if (tableVoById == null) {
                    throw new RuleExecutionException("未找到子表：" + tableName + "！");
                }
                columnNameCodeMap = CollectionUtils.isNotEmpty(tableVoById.getTableMetaJson()) ? tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
                childTableColumnNameCodeMap.put(tableName, columnNameCodeMap);
            }
            String columnName = cMatcher.group(2);
            Map tableColumnNameCodeMap = MapUtils.getMap(childTableColumnNameCodeMap, tableName);
            String childColumnCode = MapUtils.getString(tableColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(childColumnCode)) {
                throw new RuleExecutionException("子表：" + tableName + "不存在列：" + columnName);
            }
            checkColumnType(tableVoById, childColumnCode, columnName);

        }
    }

    private void checkTColumnType(String express) {
        Map<String, Map<String, String>> tTableColumnNameCodeMap = new HashMap<>();
        Map<String, Integer> tTableNameIdMap = new HashMap<>();
        Matcher tMatcher = expressTPattern.matcher(express);
        while (tMatcher.find()) {
            String tableName = tMatcher.group(1);
            Integer tTableId = tTableNameIdMap.get(tableName);
            if (tTableId == null) {
                Table tableByName = SpringUtils.getAopProxy(this).getTableByName(tableName);
                if (tableByName == null) {
                    throw new RuleExecutionException("未找到他表：" + tableName + "！");
                }
                tTableId = tableByName.getId();
                tTableNameIdMap.put(tableName, tTableId);
            }
            TableVo tableVoById = null;
            Map<String, String> columnNameCodeMap = tTableColumnNameCodeMap.get(tableName);
            if (columnNameCodeMap == null) {
                tableVoById = SpringUtils.getAopProxy(this).getTableVoById(tTableId);
                if (tableVoById == null) {
                    throw new RuleExecutionException("未找到他表：" + tableName + "！");
                }
                columnNameCodeMap = CollectionUtils.isNotEmpty(tableVoById.getTableMetaJson()) ? tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
                tTableColumnNameCodeMap.put(tableName, columnNameCodeMap);
            }
            String columnName = tMatcher.group(2);
            Map tableColumnNameCodeMap = MapUtils.getMap(tTableColumnNameCodeMap, tableName);
            String tColumnCode = MapUtils.getString(tableColumnNameCodeMap, columnName);
            if (StringUtils.isEmpty(tColumnCode)) {
                throw new RuleExecutionException("他表：" + tableName + "不存在列：" + columnName);
            }
            checkColumnType(tableVoById, tColumnCode, columnName);
        }
    }

    private Object getRuleExpressResult(Integer tableId, Object[] row, String visibleCondition) {
        Map<String, Object> context = Maps.newHashMapWithExpectedSize(2);
        context.put(RuleOperatorEnum.O.name(), row);// 当前表字段及值
        context.put(RuleConstant.RULE_EXPRESS, visibleCondition);// 规则

        return expressUtils.executeWithoutException(visibleCondition, context);
    }

    private void fetchOFCTFunctions(List<FunctionSuggestionVo> functions) {
        FunctionSuggestionVo oFunction = FunctionSuggestionVo.builder()
                .label(RuleOperatorEnum.O.name())
                .kind("monaco.languages.CompletionItemKind.Function")
                .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                .insertText(RuleOperatorEnum.O.name() + "[]")
                .detail("当前表")
                .build();
        FunctionSuggestionVo pFunction = FunctionSuggestionVo.builder()
                .label(RuleOperatorEnum.P.name())
                .kind("monaco.languages.CompletionItemKind.Function")
                .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                .insertText(RuleOperatorEnum.P.name() + "[]")
                .detail("父表")
                .build();
        FunctionSuggestionVo cFunction = FunctionSuggestionVo.builder()
                .label(RuleOperatorEnum.C.name())
                .kind("monaco.languages.CompletionItemKind.Function")
                .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                .insertText(RuleOperatorEnum.C.name() + "[]")
                .detail("子表")
                .build();
        FunctionSuggestionVo tFunction = FunctionSuggestionVo.builder()
                .label(RuleOperatorEnum.T.name())
                .kind("monaco.languages.CompletionItemKind.Function")
                .insertTextRules("monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet")
                .insertText(RuleOperatorEnum.T.name() + "[]")
                .detail("其他表")
                .build();
        functions.add(oFunction);
        functions.add(pFunction);
        functions.add(cFunction);
        functions.add(tFunction);
    }

    @Override
    public List<String> getCurrentRefColumns(Integer tableId, String columnCode) {
        RuleGraph ruleGraph = getRuleGraph();
        if (ruleGraph.isEmpty()) {
            return Collections.emptyList();
        }
        RuleVO ruleVO = new RuleVO(tableId, columnCode);
        Set set = ruleGraph.getOutDegree().get(JSON.toJSONString(ruleVO));
        if (CollectionUtils.isEmpty(set)) {
            return Collections.emptyList();
        }
        Set<String> refColumnCodes = new HashSet<>();
        for (Object obj : set) {
            String ruleString = String.valueOf(obj);
            RuleVO refRuleVO = JSON.parseObject(ruleString, RuleVO.class);
            if ((int) refRuleVO.getTableId() == tableId) {
                refColumnCodes.add(refRuleVO.getColumnCode());
            }
        }
        return new ArrayList<>(refColumnCodes);
    }

    @Override
    public RuleRefTraceViewVO refTraceView(Integer tableId, String columnCode, String val) {
        RuleRefTraceViewVO ruleRefTraceViewVO = new RuleRefTraceViewVO();
        RuleGraph ruleGraph = getRuleGraph();
        if (ruleGraph.isEmpty()) {
            return ruleRefTraceViewVO;
        }
        RuleVO ruleVO = new RuleVO(tableId, columnCode);
        String ruleVOString = JSON.toJSONString(ruleVO);
        Set refSet = ruleGraph.getOutDegree().get(ruleVOString);
        if (CollectionUtils.isNotEmpty(refSet)) {
            for (Object obj : refSet) {
                String ruleString = String.valueOf(obj);
                RuleVO refRuleVO = JSON.parseObject(ruleString, RuleVO.class);
                if ((int) refRuleVO.getTableId() != tableId) {
                    ruleRefTraceViewVO.setCanRefTrace(true);
                    break;
                }
            }
        }
        Set viewSet = ruleGraph.getInDegree().get(ruleVOString);
        if (CollectionUtils.isNotEmpty(viewSet)) {
            ruleRefTraceViewVO.setCanRefView(true);
        }
        return ruleRefTraceViewVO;
    }

}
