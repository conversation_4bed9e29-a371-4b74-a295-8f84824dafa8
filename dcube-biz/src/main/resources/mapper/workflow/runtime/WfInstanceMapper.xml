<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.workflow.runtime.mapper.WfInstanceMapper">

    <resultMap type="WfInstance" id="CubeWfInstanceResult">
        <result property="id" column="id"/>
        <result property="defineId" column="define_id"/>
        <result property="defineName" column="define_name"/>
        <result property="instanceState" column="instance_state"/>
        <result property="processStarter" column="process_starter"/>
        <result property="tableId" column="table_id"/>
        <result property="tableName" column="table_name"/>
        <result property="dataId" column="data_id"/>
        <result property="nodeId" column="node_id"/>
        <result property="nodeName" column="node_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCubeWfInstanceVo">
        select id,
               define_id,
               define_name,
               instance_state,
               process_starter,
               table_id,
               table_name,
               data_id,
               node_id,
               node_name,
               create_by,
               create_time,
               update_by,
               update_time
        from cube_wf_instance
        where del_flag = '0'
    </sql>

    <select id="selectCubeWfInstanceList" parameterType="WfInstance" resultMap="CubeWfInstanceResult">
        <include refid="selectCubeWfInstanceVo"/>
        <where>
            del_flag='0'
            <if test="defineId != null ">and define_id = #{defineId}</if>
            <if test="defineName != null  and defineName != ''">and define_name like concat('%', #{defineName}, '%')
            </if>
            <if test="instanceState != null  and instanceState != ''">and instance_state = #{instanceState}</if>
            <if test="processStarter != null  and processStarter != ''">and process_starter = #{processStarter}</if>
            <if test="tableId != null ">and table_id = #{tableId}</if>
            <if test="tableName != null  and tableName != ''">and table_name like concat('%', #{tableName}, '%')</if>
            <if test="nodeId != null and nodeId != '' ">and node_id = #{nodeId}</if>
            <if test="nodeName != null  and nodeName != ''">and node_name like concat('%', #{nodeName}, '%')</if>
        </where>
    </select>

    <select id="selectCubeWfInstanceById" parameterType="Long" resultMap="CubeWfInstanceResult">
        <include refid="selectCubeWfInstanceVo"/>
        where id = #{id} and del_flag='0'
    </select>

    <insert id="insertCubeWfInstance" parameterType="WfInstance" useGeneratedKeys="true" keyProperty="id">
        insert into cube_wf_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="defineId != null">define_id,</if>
            <if test="defineName != null">define_name,</if>
            <if test="instanceState != null">instance_state,</if>
            <if test="processStarter != null">process_starter,</if>
            <if test="tableId != null">table_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="nodeName != null">node_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="defineId != null">#{defineId},</if>
            <if test="defineName != null">#{defineName},</if>
            <if test="instanceState != null">#{instanceState},</if>
            <if test="processStarter != null">#{processStarter},</if>
            <if test="tableId != null">#{tableId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId},</if>
            <if test="nodeName != null">#{nodeName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCubeWfInstance" parameterType="WfInstance">
        update cube_wf_instance
        <trim prefix="SET" suffixOverrides=",">
            <if test="defineId != null">define_id = #{defineId},</if>
            <if test="defineName != null">define_name = #{defineName},</if>
            <if test="instanceState != null">instance_state = #{instanceState},</if>
            <if test="processStarter != null">process_starter = #{processStarter},</if>
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="nodeId != null and nodeId != ''">node_id = #{nodeId},</if>
            <if test="nodeName != null">node_name = #{nodeName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCubeWfInstanceById" parameterType="Long">
        update cube_wf_instance
        set del_flag='0'
        where id = #{id}
    </delete>

    <delete id="deleteCubeWfInstanceByIds" parameterType="String">
        update cube_wf_instance
        set del_flag='0'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>