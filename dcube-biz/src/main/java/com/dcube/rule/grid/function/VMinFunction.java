package com.dcube.rule.grid.function;

import cn.hutool.core.date.DateUtil;
import com.alibaba.qlexpress4.runtime.Parameters;
import com.alibaba.qlexpress4.runtime.QContext;
import com.alibaba.qlexpress4.runtime.function.CustomFunction;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.util.ExpressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Date;
import java.util.Map;

/**
 * 最小值
 */
@Slf4j
public class VMinFunction implements CustomFunction {

    /**
     * vmin(【它表计算列】，【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)
     *
     * @param qContext
     * @param parameters
     * @return
     * @throws Exception
     */
    @Override
    public Object call(QContext qContext, Parameters parameters) throws Throwable {
        if (parameters == null) {
            throw new RuleExecutionException("vmin规则定义的参数不能为空");
        }

        int length = parameters.size();
        if (length % 2 != 1) {
            throw new RuleExecutionException("vmin规则定义的参数格式不正确");
        }

        Long ruleId = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.RULE_ID);
        boolean useCache = true;
        Boolean cubeRule = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.CUBE_RULE);
        Map<String, Object> vminCache = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.MIN_T + ruleId);
        if (Boolean.TRUE.equals(cubeRule)) {
            useCache = vminCache != null;
        }

        if (useCache) {
            Object ret;
            if (length == 1) {
                ret = vminCache;
            } else {
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 2; i < length; i += 2) {
                    Object param = parameters.get(i).get();
                    if (param instanceof Date) {
                        stringBuilder.append(DateUtil.formatDate((Date) param));
                    } else {
                        stringBuilder.append(param);
                    }
                    if (i != length - 1) {
                        stringBuilder.append("_");
                    }
                }
                ret = MapUtils.getObject(vminCache, stringBuilder.toString());
            }
            return ret == null ? 0 : ret;
        } else {
            Object ret;
            if (length == 1) {
                ret = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.MIN_T + ruleId);
            } else {
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 2; i < length; i += 2) {
                    Object param = parameters.get(i).get();
                    if (param instanceof Date) {
                        stringBuilder.append(DateUtil.formatDate((Date) param));
                    } else {
                        stringBuilder.append(param);
                    }
                    if (i != length - 1) {
                        stringBuilder.append("_");
                    }
                }
                Map<String, Object> map = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.MIN_T + ruleId);
                ret = MapUtils.getObject(map, stringBuilder.toString());
            }
            return ret == null ? 0 : ret;
        }
    }


}
