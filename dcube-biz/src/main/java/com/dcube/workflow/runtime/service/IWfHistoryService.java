package com.dcube.workflow.runtime.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.workflow.runtime.domain.WfHistory;
import com.dcube.workflow.runtime.vo.WfHistoryVO;

import java.util.List;

/**
 * 流程历史任务Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IWfHistoryService extends IService<WfHistory> {
    /**
     * 查询流程历史任务
     *
     * @param id 流程历史任务主键
     * @return 流程历史任务
     */
    WfHistory selectCubeWfHistoryById(Long id);

    /**
     * 查询流程历史任务列表
     *
     * @param wfHistory 流程历史任务
     * @return 流程历史任务集合
     */
    List<WfHistory> selectCubeWfHistoryList(WfHistory wfHistory);

    /**
     * 新增流程历史任务
     *
     * @param wfHistory 流程历史任务
     * @return 结果
     */
    int insertCubeWfHistory(WfHistory wfHistory);

    /**
     * 修改流程历史任务
     *
     * @param wfHistory 流程历史任务
     * @return 结果
     */
    int updateCubeWfHistory(WfHistory wfHistory);

    /**
     * 批量删除流程历史任务
     *
     * @param ids 需要删除的流程历史任务主键集合
     * @return 结果
     */
    int deleteCubeWfHistoryByIds(Long[] ids);

    /**
     * 删除流程历史任务信息
     *
     * @param id 流程历史任务主键
     * @return 结果
     */
    int deleteCubeWfHistoryById(Long id);

    List<WfHistoryVO> getByInstanceId(Long instanceId);

}
