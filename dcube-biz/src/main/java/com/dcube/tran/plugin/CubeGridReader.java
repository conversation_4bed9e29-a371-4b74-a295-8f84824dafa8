package com.dcube.tran.plugin;

import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.common.dto.ReportDto;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.spi.CubeMetaData;
import com.dcube.cube.spi.CubeSchema;
import com.dcube.cube.spi.CubeServer;
import com.dcube.tran.element.CubeRecord;
import com.dcube.tran.element.Record;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class CubeGridReader extends Reader {

    private DimTableInfoVo dimTableInfoVo;

    private CubeServer cubeServer;

    private Map<String, List<String>> filterDims;

    private ReportDto reportDto;

    private final long waitSeconds = 10;

    private CubeGridReader() {
    }

    public CubeGridReader(DimTableInfoVo dimTableInfoVo, CubeServer cubeServer, ReportDto reportDto, Map<String, List<String>> filterDims) {
        this.dimTableInfoVo = dimTableInfoVo;
        this.cubeServer = cubeServer;
        this.reportDto = reportDto;
        this.filterDims = filterDims;
    }

    @Override
    public void startRead(RecordSender recordSender) {
        log.info("============================================={}==============================================", "startRead开始");
        try {
            if (cubeServer == null) {
                List<String> dimIdList = dimTableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
                List<String> indIdList = dimTableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
                CubeMetaData cubeMetaData = new CubeMetaData(dimTableInfoVo.getId().toString(), dimIdList, indIdList);
                cubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);
            }

            List<FactTable.Record> recordList = cubeServer.filterRecord(filterDims);
            if (CollectionUtils.isNotEmpty(recordList)) {
                reportDto.setTotalRecord((long) recordList.size());
                for (FactTable.Record record : recordList) {
                    this.transportOneRecord(recordSender, record);
                }
            }
            getTaskHelp().setReaderStatus(true);
        } catch (Exception e) {
            getTaskHelp().setReaderStatus(false);
            getTaskHelp().setMsg(e.getMessage());
            log.error(e.getMessage(), e);
            throw DataTranException.asDataTranException(CommonErrorCode.READ_CUBE_QUERY, "查询维度错误");
        } finally {
            log.info("============================================={}==============================================", "startRead结束");
        }
    }

    private Record transportOneRecord(RecordSender recordSender, FactTable.Record rs) {
        Record record = buildRecord(rs);
        while (getTaskHelp().checkStatus()) {
            if (recordSender.sendToWriter(record, waitSeconds)) {
                break;
            }
        }
        return record;
    }

    private Record buildRecord(FactTable.Record rs) {
        CubeRecord record = new CubeRecord(rs);
        return record;
    }
}