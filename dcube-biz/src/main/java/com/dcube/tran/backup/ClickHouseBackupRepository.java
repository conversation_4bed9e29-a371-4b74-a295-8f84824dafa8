package com.dcube.tran.backup;

import cn.hutool.core.date.DateUtil;
import com.dcube.biz.constant.enums.TableBackupColumnTypeEnum;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class ClickHouseBackupRepository extends AbstractBackupRepository {

    private static final String COLUMN_SQL_FORMAT = "%s %s comment '%s'";
    private static final String ADD_COLUMN_SQL_FORMAT = "alter table %s add column " + COLUMN_SQL_FORMAT;
    private static final String MODIFY_COLUMN_SQL_FORMAT = "alter table %s modify column %s %s";
    private static final String COMMENT_COLUMN_SQL_FORMAT = "alter table %s comment column %s '%s'";

    ClickHouseBackupRepository(String dbType, SourceConfigJson config, String tableName, Map<String, TableBackupConfig> backupConfigMap, int tableLevel, String tableComment) {
        super(dbType, config, tableName, backupConfigMap, tableLevel, tableComment);
    }

    @Override
    public String getIntColumn() {
        return "Int32";
    }

    @Override
    public String getBigintColumn() {
        return "Int64";
    }

    @Override
    public String getStringColumn() {
        return "String";
    }

    @Override
    public String getNumericColumn() {
        return "Decimal(%s,%s)";
    }

    @Override
    public String getDatetimeColumn() {
        return "DateTime64";
    }

    @Override
    public void createTable() {
        List<String> columnList = buildCreateTableColumns();
        int tableLevel = getTableLevel();
        List<String> primaryKeyList = new ArrayList<>(tableLevel);
        Map<String, TableBackupConfig> backupConfigMap = getBackupConfigMap();
        for (Map.Entry<String, TableBackupConfig> entry : backupConfigMap.entrySet()) {
            TableBackupConfig tableBackupConfig = entry.getValue();
            if (tableBackupConfig.isPrimaryKey()) {
                primaryKeyList.add(StringUtils.lowerCase(entry.getKey()));
            }
        }
        String sql = String.format("create table %s (%s) ENGINE = MergeTree() ORDER BY (base_data_dt,backup_version_name,%s) COMMENT '%s'", getTableName(), String.join(",", columnList), String.join(",", primaryKeyList), getTableComment());
        super.getAbstractRepository().executeSql(sql);
    }

    private List<String> buildCreateTableColumns() {
        Map<String, TableBackupConfig> backupConfigMap = getBackupConfigMap();
        List<String> columns = new ArrayList<>(backupConfigMap.size());
        for (Map.Entry<String, TableBackupConfig> entry : backupConfigMap.entrySet()) {
            TableBackupConfig tableBackupConfig = entry.getValue();
            String columnName = tableBackupConfig.getBackupColumnName().toLowerCase();
            TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
            if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
                tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
            }
            switch (columnType) {
                case INTEGER:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, tableBackupConfig.isPrimaryKey() ? getIntColumn() : "Nullable(" + getIntColumn() + ")", tableBackupConfig.getRemark()));
                    break;
                case BIGINT:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, tableBackupConfig.isPrimaryKey() ? getBigintColumn() : "Nullable(" + getBigintColumn() + ")", tableBackupConfig.getRemark()));
                    break;
                case VARCHAR:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, tableBackupConfig.isPrimaryKey() ? getStringColumn() : "Nullable(" + getStringColumn() + ")", tableBackupConfig.getRemark()));
                    break;
                case NUMERIC:
                    String _columnType = String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE);
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, tableBackupConfig.isPrimaryKey() ? _columnType : "Nullable(" + _columnType + ")", tableBackupConfig.getRemark()));
                    break;
                case DATETIME:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, tableBackupConfig.isPrimaryKey() ? getDatetimeColumn() : "Nullable(" + getDatetimeColumn() + ")", tableBackupConfig.getRemark()));
                    break;
                default:
                    log.error("Unsupported type: " + columnType);
                    break;
            }
        }
        // 添加备份版本名称字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "backup_version_name", String.format(getStringColumn()), "备份版本名称"));
        // 添加数据日期字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "base_data_dt", String.format(getDatetimeColumn()), "数据日期"));
        return columns;
    }

    @Override
    public String genAddColumnSql(TableBackupConfig tableBackupConfig) {
        return genColumnSql(tableBackupConfig, ADD_COLUMN_SQL_FORMAT);
    }

    @Override
    public String genRenameColumnSql(String oldColumnName, String newColumnName) {
        return String.format("ALTER TABLE %s RENAME COLUMN %s TO %s", getTableName(), oldColumnName, newColumnName);
    }

    public String genModifyColumnSql(TableBackupConfig tableBackupConfig) {
        return genColumnSql(tableBackupConfig, MODIFY_COLUMN_SQL_FORMAT);
    }

    private String genColumnSql(TableBackupConfig tableBackupConfig, String format) {
        String sql = "";
        TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
        String columnName = tableBackupConfig.getBackupColumnName().toLowerCase();
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }
        switch (columnType) {
            case INTEGER:
                sql = String.format(format, getTableName(), columnName, getIntColumn());
                break;
            case BIGINT:
                sql = String.format(format, getTableName(), columnName, tableBackupConfig.isPrimaryKey() ? getBigintColumn() : "Nullable(" + getBigintColumn() + ")");
                break;
            case VARCHAR:
                sql = String.format(format, getTableName(), columnName, tableBackupConfig.isPrimaryKey() ? getStringColumn() : "Nullable(" + getStringColumn() + ")");
                break;
            case NUMERIC:
                String _columnType = String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE);
                sql = String.format(format, getTableName(), columnName, tableBackupConfig.isPrimaryKey() ? _columnType : "Nullable(" + _columnType + ")");
                break;
            case DATETIME:
                sql = String.format(format, getTableName(), columnName, tableBackupConfig.isPrimaryKey() ? getDatetimeColumn() : "Nullable(" + getDatetimeColumn() + ")");
                break;
            default:
                log.error("Unsupported type: " + columnType);
                break;
        }
        return sql;
    }

    @Override
    public boolean checkColumnTypeName(List<String> sqlList, String columnType, TableBackupConfig tableBackupConfig, int columnSize, String remarks) {
        boolean flag = false;
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }
        switch (tableBackupConfig.getColumnType()) {
            case INTEGER:
                flag = columnType.equalsIgnoreCase("Int32") || columnType.equalsIgnoreCase("Nullable(Int32)");
                break;
            case BIGINT:
                flag = columnType.equalsIgnoreCase("Int64") || columnType.equalsIgnoreCase("Nullable(Int64)");
                break;
            case VARCHAR:
                flag = columnType.equalsIgnoreCase("String") || columnType.equalsIgnoreCase("Nullable(String)");
                break;
            case NUMERIC:
                flag = StringUtils.containsAnyIgnoreCase(columnType, "Decimal");
                break;
            case DATETIME:
                flag = StringUtils.containsAnyIgnoreCase(columnType, "DateTime64");
                break;
        }
        if (flag) {
            flag = StringUtils.equals(remarks, tableBackupConfig.getRemark());
            if (!flag) {
                if (sqlList != null) {
                    sqlList.add(java.lang.String.format(COMMENT_COLUMN_SQL_FORMAT, getTableName(), tableBackupConfig.getBackupColumnName().toLowerCase(), tableBackupConfig.getRemark()));
                }
            }
        } else {
            if (sqlList != null) {
                sqlList.add(genModifyColumnSql(tableBackupConfig));
            }
        }
        return flag;
    }

    @Override
    public String genTableCommentSql() {
        return StringUtils.EMPTY;
    }

    @Override
    public void deleteData(String backupTableName, String versionName, Date baseDataDt) {
        super.getAbstractRepository().executeSql(String.format("ALTER TABLE %s DELETE WHERE backup_version_name = '%s' and base_data_dt = '%s'", backupTableName.toLowerCase(), versionName, DateUtil.formatDate(baseDataDt)));
    }

}
