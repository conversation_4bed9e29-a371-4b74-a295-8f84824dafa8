package com.dcube.workflow.runtime.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程任务
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class WfTaskDTO extends BaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long instanceId;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long defineId;

    /**
     * 流程定义名称
     */
    @Schema(description = "流程定义名称")
    private String defineName;

    /**
     * 流程状态列编码
     */
    @Schema(description = "流程状态列编码")
    private String dataId;

    /**
     * 是否完成
     */
    @Schema(description = "是否完成")
    private String taskDone;


    /**
     * 耗时
     */
    @Schema(description = "耗时")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long cost;

    /**
     * 制单人
     */
    @Schema(description = "制单人")
    private String processStarter;

    /**
     * 表id
     */
    @Schema(description = "表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 表名
     */
    @Schema(description = "表名")
    private String tableName;

    /**
     * 所属节点id
     */
    @Schema(description = "所属节点id")
    private String nodeId;

    /**
     * 所属节点名称
     */
    @Schema(description = "所属节点名称")
    private String nodeName;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String userId;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String userName;

    /**
     * 审批意见内容
     */
    @Schema(description = "审批意见内容")
    private String commentContent;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "流程结束")
    private Boolean processDone;

    @Schema(description = "下一个线id")
    private String nextEdgeId;
    /**
     * 提交路径名称
     */
    @Schema(description = "提交路径名称")
    private String nextEdgeName;
    /**
     * 下一节点id
     */
    @Schema(description = "下一节点id")
    private String nextNodeId;
    /**
     * 下一节点名称
     */
    @Schema(description = "下一节点名称")
    private String nextNodeName;

    @Schema(description = "执行类型（提交SUBMIT、同意AGREE、撤回WITHDRAW、退回RETURN）")
    private String executeType;

    /**
     * 流程实例类型
     */
    @Schema(description = "流程实例类型")
    private WfInstanceTypeEnums flowType = WfInstanceTypeEnums.TABLE;

    /**
     * 父节点ID
     */
    @Schema(description = "父节点ID")
    private String parentNodeId;
    /**
     * 归口维度流程定义id
     */
    @Schema(description = "归口维度流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long centralizeDimDefineId;
}
