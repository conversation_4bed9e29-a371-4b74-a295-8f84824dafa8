package com.dcube.workflow.runtime.vo;

import com.dcube.biz.base.BaseVo;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Schema(description = "流程任务视图对象")
public class ViewNodeStateVO extends BaseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点id
     */
    @Schema(description = "节点id")
    private String nodeId;

    /**
     * 节点名称
     */
    @Schema(description = "节点名称")
    private String nodeName;

//    /**
//     * 流程实例id
//     */
//    @Schema(description = "流程实例id")
//    @JsonSerialize(using = ToStringSerializer.class)
//    private Long instanceId;

//    /**
//     * 任务id
//     */
//    @Schema(description = "任务id")
//    @JsonSerialize(using = ToStringSerializer.class)
//    private Long taskId;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String userName;

    /**
     * 任务状态（待提交、待审批、已审批、已撤回、已退回、未处理）
     */
    @Schema(description = "任务状态（待提交、待审批、已审批、已撤回、已退回、未处理）")
    private WfTaskStateEnums taskState;

    /**
     * 任务状态名称（待提交、待审批、已审批、已撤回、已退回、未处理）
     */
    @Schema(description = "任务状态名称")
    private String taskStateName;

    @Schema(description = "祖级列表")
    private String ancestors;

    /**
     * 上级维度成员ID
     */
    @Schema(description = "上级维度成员ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;
    /**
     * 是否叶子
     */
    @Schema(description = "是否叶子")
    private String isLeaf;
    /**
     * 归口维度
     */
    @Schema(description = "归口维度")
    private List<WfDimDefineCentralize> centralizeList;

}
