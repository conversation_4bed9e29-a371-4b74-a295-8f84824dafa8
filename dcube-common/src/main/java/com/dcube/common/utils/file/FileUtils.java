package com.dcube.common.utils.file;

import com.dcube.common.config.DCubeConfig;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.DateUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.uuid.IdUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件处理工具类
 */
@Slf4j
public class FileUtils {
    public static String FILENAME_PATTERN = "[a-zA-Z0-9_\\-\\|\\.\\u4e00-\\u9fa5]+";

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os       输出流
     * @return
     */
    public static void writeBytes(String filePath, OutputStream os) throws IOException {
        FileInputStream fis = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new FileNotFoundException(filePath);
            }
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int length;
            while ((length = fis.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } finally {
            IOUtils.close(os);
            IOUtils.close(fis);
        }
    }

    /**
     * 写数据到文件中
     *
     * @param data 数据
     * @return 目标文件
     * @throws IOException IO异常
     */
    public static String writeImportBytes(byte[] data) throws IOException {
        return writeBytes(data, DCubeConfig.getImportPath());
    }

    /**
     * 写数据到文件中
     *
     * @param data      数据
     * @param uploadDir 目标文件
     * @return 目标文件
     * @throws IOException IO异常
     */
    public static String writeBytes(byte[] data, String uploadDir) throws IOException {
        FileOutputStream fos = null;
        String pathName = "";
        try {
            String extension = getFileExtendName(data);
            pathName = DateUtils.datePath() + "/" + IdUtils.fastUUID() + "." + extension;
            File file = FileUploadUtils.getAbsoluteFile(uploadDir, pathName);
            fos = new FileOutputStream(file);
            fos.write(data);
        } finally {
            IOUtils.close(fos);
        }
        return FileUploadUtils.getPathFileName(uploadDir, pathName);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean flag = false;
        File file = new File(filePath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            flag = file.delete();
        }
        return flag;
    }

    /**
     * 文件名称验证
     *
     * @param filename 文件名称
     * @return true 正常 false 非法
     */
    public static boolean isValidFilename(String filename) {
        return filename.matches(FILENAME_PATTERN);
    }

    /**
     * 检查文件是否可下载
     *
     * @param resource 需要下载的文件
     * @return true 正常 false 非法
     */
    public static boolean checkAllowDownload(String resource) {
        // 禁止目录上跳级别
        if (StringUtils.contains(resource, "..")) {
            return false;
        }

        // 检查允许下载的文件规则
        return ArrayUtils.contains(MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, FileTypeUtils.getFileType(resource));

        // 不在允许下载的文件规则
    }

    /**
     * 下载文件名重新编码
     *
     * @param request  请求对象
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String setFileDownloadHeader(HttpServletRequest request, String fileName) throws UnsupportedEncodingException {
        final String agent = request.getHeader("USER-AGENT");
        String filename = fileName;
        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
            filename = filename.replace("+", " ");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else if (agent.contains("Chrome")) {
            // google浏览器
            filename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        }
        return filename;
    }

    /**
     * 下载文件名重新编码
     *
     * @param response     响应对象
     * @param realFileName 真实文件名
     */
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName) throws UnsupportedEncodingException {
        String percentEncodedFileName = percentEncode(realFileName);

        String contentDispositionValue = "attachment; filename=" +
                percentEncodedFileName +
                ";" +
                "filename*=" +
                "utf-8''" +
                percentEncodedFileName;

        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
        response.setHeader("Content-disposition", contentDispositionValue);
        response.setHeader("download-filename", percentEncodedFileName);
    }

    /**
     * 百分号编码工具方法
     *
     * @param s 需要百分号编码的字符串
     * @return 百分号编码后的字符串
     */
    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8);
        return encode.replaceAll("\\+", "%20");
    }

    /**
     * 获取图像后缀
     *
     * @param photoByte 图像数据
     * @return 后缀名
     */
    public static String getFileExtendName(byte[] photoByte) {
        String strFileExtendName = "jpg";
        if ((photoByte[0] == 71) && (photoByte[1] == 73) && (photoByte[2] == 70) && (photoByte[3] == 56)
                && ((photoByte[4] == 55) || (photoByte[4] == 57)) && (photoByte[5] == 97)) {
            strFileExtendName = "gif";
        } else if ((photoByte[6] == 74) && (photoByte[7] == 70) && (photoByte[8] == 73) && (photoByte[9] == 70)) {
            strFileExtendName = "jpg";
        } else if ((photoByte[0] == 66) && (photoByte[1] == 77)) {
            strFileExtendName = "bmp";
        } else if ((photoByte[1] == 80) && (photoByte[2] == 78) && (photoByte[3] == 71)) {
            strFileExtendName = "png";
        }
        return strFileExtendName;
    }

    /**
     * 获取文件名称 /profile/upload/2022/04/16/ruoyi.png -- ruoyi.png
     *
     * @param fileName 路径名称
     * @return 没有文件路径的名称
     */
    public static String getName(String fileName) {
        if (fileName == null) {
            return null;
        }
        int lastUnixPos = fileName.lastIndexOf('/');
        int lastWindowsPos = fileName.lastIndexOf('\\');
        int index = Math.max(lastUnixPos, lastWindowsPos);
        return fileName.substring(index + 1);
    }

    /**
     * 获取不带后缀文件名称 /profile/upload/2022/04/16/ruoyi.png -- ruoyi
     *
     * @param fileName 路径名称
     * @return 没有文件路径和后缀的名称
     */
    public static String getNameNotSuffix(String fileName) {
        if (fileName == null) {
            return null;
        }

        String baseName = FilenameUtils.getBaseName(fileName);
        return baseName;
    }

    public static File[] lsFiles(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return null;
        } else {
            File file = new File(filePath);
            return file.listFiles();
        }
    }

    public static File[] lsFilesWithFilter(String filePath, String filterSuffix) {
        // 检查路径是否为空或空白
        if (StringUtils.isEmpty(filePath)) {
            log.error("文件路径不能为空");
            throw new ServiceException("文件路径不能为空");
        }

        // 检查路径是否存在
        File path = new File(filePath);
        if (!path.exists() || !path.isDirectory()) {
            log.error("指定的路径:[{}]不是有效的路径", filePath);
            throw new ServiceException(String.format("指定的路径:[%s]不是有效的路径", filePath));
        }

        // 过滤
        File[] files = path.listFiles((dir, name) -> name.endsWith(filterSuffix));

        return files != null ? files : new File[0];
    }

    /**
     * 根据指定的范围读取文件的指定行数，并将结果以字符串形式返回。
     *
     * @param startLine 范围的起始行号
     * @param endLine   范围的结束行号
     * @param filePath  文件的路径
     * @param charset
     * @return 范围内所有行的字符串形式
     * @throws ServiceException 如果参数无效或文件读取失败，则抛出此异常
     */
    public static String readLinesByRange(long startLine, long endLine, String filePath, Charset charset) {
        // 参数检查
        if (startLine <= 0 || endLine < startLine || filePath == null || filePath.isEmpty()) {
            log.error("参数错误: 开始行必须小于等于结束行且文件路径不能为空。");
            throw new ServiceException("参数错误: 开始行必须小于等于结束行且文件路径不能为空。");
        }

        // 路径检查
        Path path = Paths.get(filePath);
        if (!Files.exists(path) || !Files.isReadable(path)) {
            log.error("文件路径无效或不可读: {}", filePath);
            throw new ServiceException("文件路径无效或不可读: " + filePath);
        }

        // 使用Files.lines流按行读取文件
        try (Stream<String> lines = Files.lines(path, charset)) {
            // 这里读取文件时不包含结束行，所以endLine需要+1
            return lines
                    .skip(startLine - 1)                    // 跳过前startLine-1行
                    .limit(endLine - startLine + 1)         // 读取endLine - startLine + 1行
                    .collect(Collectors.joining("\n"));     // 按换行拼接为单个字符串
        } catch (IOException e) {
            // 使用日志记录工具记录异常，例如：
            log.error("文件读取失败: {}", filePath, e);
            throw new ServiceException("文件读取失败: " + filePath, e);
        }
    }

    public static Charset detectCharset(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            throw new ServiceException("待检测的文件无效或不存在：" + (file != null ? file.getAbsolutePath() : "null"));
        }

        // 常见编码集，按常用顺序排列
        Charset[] charsets = {
                StandardCharsets.UTF_8,
                Charset.forName("GBK"),
                StandardCharsets.ISO_8859_1
        };

        for (Charset charset : charsets) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), charset))) {
                // 尝试读取一行，能读到说明该编码可能是正确的
                if (reader.readLine() != null) {
                    return charset;
                }
            } catch (IOException e) {
                // 忽略异常，继续尝试下一个编码
            }
        }

        // 默认返回 UTF-8
        return StandardCharsets.UTF_8;
    }

    public static Map<String, File> lsFilesWithFilter(String filePath, Set<String> tableNames, String filterSuffix, int fileNamePrefixNum, int fileNameSuffixNum) {
        // 检查路径是否为空或空白
        if (StringUtils.isEmpty(filePath)) {
            log.error("文件路径不能为空");
            throw new ServiceException("文件路径不能为空");
        }

        // 检查路径是否存在
        File path = new File(filePath);
        if (!path.exists() || !path.isDirectory()) {
            log.error("指定的路径:[{}]不是有效的路径", filePath);
            throw new ServiceException(String.format("指定的路径:[%s]不是有效的路径", filePath));
        }

        // 获取所有文件
        File[] files = path.listFiles((dir, name) -> name.endsWith(filterSuffix));
        if (files == null || files.length == 0) {
            return Collections.emptyMap();
        }

        // 构建Map
        Map<String, File> result = Maps.newLinkedHashMapWithExpectedSize(files.length);
        for (File file : files) {
            String fileName = FilenameUtils.getBaseName(file.getName());
            if (fileNamePrefixNum > 0) {
                fileName = fileName.substring(fileNamePrefixNum);
            }
            if (fileNameSuffixNum > 0) {
                fileName = fileName.substring(0, fileNameSuffixNum);
            }
            if (tableNames.contains(fileName)) {
                result.put(fileName, file);
            }
        }

        return result;
    }

}
