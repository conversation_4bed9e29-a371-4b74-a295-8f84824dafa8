<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.workflow.runtime.mapper.WfTaskMapper">

    <resultMap type="WfTask" id="CubeWfTaskResult">
        <result property="id" column="id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="defineId" column="define_id"/>
        <result property="defineName" column="define_name"/>
        <result property="dataId" column="data_id"/>
        <result property="taskDone" column="task_done"/>
        <result property="processStarter" column="process_starter"/>
        <result property="tableId" column="table_id"/>
        <result property="tableName" column="table_name"/>
        <result property="nodeId" column="node_id"/>
        <result property="nodeName" column="node_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="commentContent" column="comment_content"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="centralizeDimDefineId" column="centralize_dim_define_id"/>
    </resultMap>

    <sql id="selectCubeWfTaskVo">
        select id,
               instance_id,
               define_id,
               define_name,
               data_id,
               task_done,
               process_starter,
               table_id,
               table_name,
               node_id,
               node_name,
               user_id,
               user_name,
               comment_content,
               create_by,
               create_time,
               update_by,
               update_time
        from cube_wf_task
        where del_flag = '0'
    </sql>

    <select id="selectCubeWfTaskList" resultType="com.dcube.workflow.runtime.vo.WfTaskVO">
        select t.id,
        t.instance_id,
        t.define_id,
        wd.flow_name as define_name,
        wd.flow_type as flow_type,
        t.data_id,
        t.task_done,
        t.process_starter,
        t.table_id,
        COALESCE(d.table_name, dt.table_name) as table_name,
        t.node_id,
        t.node_name,
        t.user_id,
        t.user_name,
        t.comment_content,
        t.approve_time,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.task_state,
        t.centralize_dim_define_id,
        i.instance_state,
        t.cost
        from cube_wf_task t
        left join cube_wf_instance i on t.instance_id = i.id
        left join cube_wf_define wd on t.define_id = wd.id
        left join cube_2d_table d on t.table_id = d.id
        left join cube_dim_table dt on t.table_id = dt.id
        <where>
            t.del_flag='0'
            <if test="instanceId != null">and t.instance_id = #{instanceId}</if>
            <!--            <if test="defineId != null ">and t.define_id = #{defineId}</if>-->
            <!--            <if test="defineName != null  and defineName != ''">and t.define_name like concat('%', #{defineName}, '%')-->
            <!--     </if>-->
            <!--            <if test="dataId != null  and dataId != ''">and t.data_id = #{dataId}</if>-->
            <if test="taskDone != null  and taskDone != ''">and t.task_done = #{taskDone}</if>
            <!--            <if test="processStarter != null  and processStarter != ''">and t.process_starter = #{processStarter}</if>-->
            <!--            <if test="tableId != null ">and t.table_id = #{tableId}</if>-->
            <if test="tableName != null and tableName != ''">and (d.table_name like concat('%', #{tableName}, '%') or
                dm.table_name like concat('%', #{tableName}, '%'))
            </if>
            <if test="processDone != null and processDone == true">and i.instance_state = '2'</if>
            <!--            <if test="nodeId != null and nodeId != '' ">and t.node_id = #{nodeId}</if>-->
            <!--            <if test="nodeName != null  and nodeName != ''">and t.node_name like concat('%', #{nodeName}, '%')</if>-->
            <if test="userId != null  and userId != ''">and t.user_id = #{userId}</if>
            <if test="parentNodeId != null  and parentNodeId != ''">and t.parent_node_id = #{parentNodeId}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectCubeWfTaskById" parameterType="Long" resultMap="CubeWfTaskResult">
        <include refid="selectCubeWfTaskVo"/>
        where id = #{id} and del_flag='0'
    </select>

    <select id="selectCubeWfTaskCount" resultType="java.lang.Integer">
        select count(*) from cube_wf_task
        <where>
            del_flag='0'
            <!--            <if test="instanceId != null ">and t.instance_id = #{instanceId}</if>-->
            <!--            <if test="defineId != null ">and t.define_id = #{defineId}</if>-->
            <!--            <if test="defineName != null  and defineName != ''">and t.define_name like concat('%', #{defineName}, '%')-->
            <!--     </if>-->
            <!--            <if test="dataId != null  and dataId != ''">and t.data_id = #{dataId}</if>-->
            <if test="taskDone != null  and taskDone != ''">and t.task_done = #{taskDone}</if>
            <!--            <if test="processStarter != null  and processStarter != ''">and t.process_starter = #{processStarter}</if>-->
            <!--            <if test="tableId != null ">and t.table_id = #{tableId}</if>-->
            <if test="tableName != null and tableName != ''">and d.table_name like concat('%', #{tableName}, '%')</if>
            <if test="processDone != null and processDone == true">and i.instance_state = '2'</if>
            <!--            <if test="nodeId != null and nodeId != '' ">and t.node_id = #{nodeId}</if>-->
            <!--            <if test="nodeName != null  and nodeName != ''">and t.node_name like concat('%', #{nodeName}, '%')</if>-->
            <if test="userId != null  and userId != ''">and t.user_id = #{userId}</if>
        </where>
    </select>
    <select id="selectWfTaskCountByUserId" resultType="java.util.Map">
        select task_done, count(*) as val
        from cube_wf_task
        where user_id = #{userId}
          and del_flag = '0'
        group by task_done
    </select>


    <insert id="insertCubeWfTask" parameterType="WfTask" useGeneratedKeys="true" keyProperty="id">
        insert into cube_wf_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="instanceId != null">instance_id,</if>
            <if test="defineId != null">define_id,</if>
            <if test="defineName != null">define_name,</if>
            <if test="dataId != null">data_id,</if>
            <if test="taskDone != null">task_done,</if>
            <if test="processStarter != null">process_starter,</if>
            <if test="tableId != null">table_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="nodeName != null">node_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="instanceId != null">#{instanceId},</if>
            <if test="defineId != null">#{defineId},</if>
            <if test="defineName != null">#{defineName},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="taskDone != null">#{taskDone},</if>
            <if test="processStarter != null">#{processStarter},</if>
            <if test="tableId != null">#{tableId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId},</if>
            <if test="nodeName != null">#{nodeName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCubeWfTask" parameterType="WfTask">
        update cube_wf_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="defineId != null">define_id = #{defineId},</if>
            <if test="defineName != null">define_name = #{defineName},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="taskDone != null">task_done = #{taskDone},</if>
            <if test="processStarter != null">process_starter = #{processStarter},</if>
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="nodeId != null and nodeId != ''">node_id = #{nodeId},</if>
            <if test="nodeName != null">node_name = #{nodeName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCubeWfTaskById" parameterType="Long">
        update cube_wf_task
        set del_flag='0'
        where id = #{id}
    </delete>

    <delete id="deleteCubeWfTaskByIds" parameterType="String">
        update cube_wf_task
        set del_flag='0'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>