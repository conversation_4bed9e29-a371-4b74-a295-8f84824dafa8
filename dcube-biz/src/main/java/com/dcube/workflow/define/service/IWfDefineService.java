package com.dcube.workflow.define.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.DimDirectory;
import com.dcube.biz.domain.DimTable;
import com.dcube.biz.domain.Table;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.workflow.define.constants.enums.WfDefineFlowTypeEnums;
import com.dcube.workflow.define.domain.WfDefine;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.define.vo.WfDimDefineDetailVO;

import java.util.List;
import java.util.Set;

/**
 * 流程定义Service接口
 *
 * @date 2024-02-25
 */
public interface IWfDefineService extends IService<WfDefine> {
    /**
     * 查询流程定义
     *
     * @param id 流程定义主键
     * @return 流程定义
     */
    WfDefine selectCubeWfDefineById(Long id);

    /**
     * 查询流程定义列表
     *
     * @param wfDefine 流程定义
     * @return 流程定义集合
     */
    List<WfDefine> selectCubeWfDefineList(WfDefine wfDefine);

    /**
     * 新增流程定义
     *
     * @param wfDefine 流程定义
     * @return 结果
     */
    Boolean insertCubeWfDefine(WfDefine wfDefine);

    /**
     * 修改流程定义
     *
     * @param wfDefine 流程定义
     * @return 结果
     */
    Boolean updateCubeWfDefine(WfDefine wfDefine);

    /**
     * 批量删除流程定义
     *
     * @param ids 需要删除的流程定义主键集合
     * @return 结果
     */
    int deleteCubeWfDefineByIds(Long[] ids);

    /**
     * 删除流程定义信息
     *
     * @param id 流程定义主键
     * @return 结果
     */
    int deleteCubeWfDefineById(Long id);

    List<Table> get2dTable(TableListQuery query);

    List<TableMetaJson> get2dTableMeta(Integer tableId);

    WfDefine getByTableId(Integer tableId);

    WfDefine getByTableId(Integer tableId, boolean needFlowChart);

    List<FlowChart.NodeDTO> getNextNodes(Long defineId, String nodeId);

    FlowChart.NodeDTO getStartNode(Long defineId);

    List<TableMetaJson> getUnitTableMeta(Integer id);

    List<DimTable> getCubeTableList(DimTableListQuery query);

    List<DimDirectory> getDimListByDimTableId(Integer dimTableId);

    List<WfDimDefineDetailVO> getByDefineId(Long defineId, String dimName);

    List<WfDimDefineDetailVO> getByDefineId(Long defineId, Set<String> filterDimIds);

    List<WfDimDefineDetailVO> getByDefineId(Long defineId, String dimName, boolean withUserName, Set<String> filterDimIds);

    void deleteWfDefine(List<Integer> idList, WfDefineFlowTypeEnums wfDefineFlowTypeEnums);

}
