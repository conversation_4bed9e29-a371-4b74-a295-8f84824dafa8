package com.dcube.tran.plugin;

import com.dcube.common.dto.ReportDto;
import com.dcube.common.report.ReportTool;
import com.dcube.common.report.TaskReport;
import com.dcube.grid.MemorySchema;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.element.Column;
import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.exchanger.RecordReceiver;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Deprecated
@Slf4j
public class GridDerivationWriter extends GridWriter {

    public GridDerivationWriter(String tableName, TableMetaData tableMetaData, ReportDto reportDto) {
        super(tableName, tableMetaData, reportDto);
    }

    @Override
    public void startWrite(RecordReceiver recordReceiver) {
        List<Record> writeBuffer = new ArrayList<>(BATCH_SIZE);
        MemorySchema.getDatabase().createTable(tableMetaData);
        int rowSize = tableMetaData.getColumnTypes().size();
        try {
            List<String> columnNames = tableMetaData.getColumnNames();
            List<String> columnNameComments = tableMetaData.getColumnNameComments();
            List<Class<?>> columnTypes = tableMetaData.getColumnTypes();
            Record record;
            while (getTaskHelp().checkStatus()) {
                record = recordReceiver.getFromReader(10);
                if (record != null) {
                    if (record instanceof TerminateRecord) {
                        break;
                    }
                    Object[] row = new Object[rowSize];
                    for (int i = 0, size = columnNames.size(); i < size; i++) {
                        String columnName = columnNames.get(i);
                        boolean match = false;
                        for (Column column : record.getColumnList()) {
                            if (columnName.equals(column.getColumnName())) {
                                row[i] = getValue(columnNameComments.get(i), columnTypes.get(i), column);
                                match = true;
                                break;
                            }
                        }
                        if (!match) {
                            row[i] = null;
                        }
                    }

                    MemorySchema.getDatabase().insertData(tableName, row);

                    writeBuffer.add(record);
                    if (writeBuffer.size() >= BATCH_SIZE) {
                        // 记录报告进度
                        ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                        writeBuffer.clear();
                    }
                }
            }
            if (getTaskHelp().checkStatus() && !writeBuffer.isEmpty()) {
                // 记录报告进度
                ReportTool.appendRecord(this.reportDto, writeBuffer.size());
                writeBuffer.clear();
            }
            getTaskHelp().setWriterStatus(true);
            this.reportDto.setTotalRecord((long) MemorySchema.getDatabase().getTableData(tableName).length);
        } catch (Exception e) {
            getTaskHelp().setWriterStatus(false);
            getTaskHelp().setMsg(e.getLocalizedMessage());
            log.error(CommonErrorCode.RUNTIME_ERROR + " PreWriter is error.", e);
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "PreWriter is error.", e);
        } finally {
            writeBuffer.clear();
            TaskReport.finished(this.reportDto);
        }
    }
}